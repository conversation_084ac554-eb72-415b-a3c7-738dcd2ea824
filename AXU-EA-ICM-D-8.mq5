//+------------------------------------------------------------------+
//|                                XAU_VolatilitySpike_Scalper_EA.mq5 |
//|                          【XAU黄金专用】波动异常反转策略EA - 移动止损优化版 |
//|                     经16轮优化 | 反转策略二阶段移动止损 | 专业级 |
//+------------------------------------------------------------------+
#property copyright "XAU Gold Trading Strategy - Trailing Stop Optimized"
#property version   "3.18"
#property strict

// 输入参数
input double   Risk_Percent_Per_Trade    = 1.5;     // 每笔风险控制百分比（最终优化：1.5%，平衡收益与风险）
input double   Spike_Range_Multiplier    = 1.3;     // 波动异常触发倍数（修改说明：从1.5调整至1.3，进一步提高信号敏感度）
input int      Spike_Min_Points          = 115;     // 波动最小阈值（点）（修改说明：从165调整至115点，进一步提高信号敏感度）
input int      Spike_Max_Points          = 600;     // 波动最大阈值（点）（修改说明：从700降至600，更严格避免极端波动下的反转失效）
input bool     Volume_Spike_Enabled      = true;    // 是否启用成交量过滤（优化：启用，提升信号质量）
input int      Volume_MA_Period          = 10;      // 成交量均值周期（标准设置：10周期）
input double   Volume_Threshold_Mult     = 1.05;    // 成交量倍数阈值（修改说明：从1.15调整至1.05，提高成交量信号敏感度）
input int      StopLoss_Points           = 125;     // 止损点位（修改说明：从100调整至125点，给价格更多波动空间）
// input int      Reverse_StopLoss_Points   = 65;      // 反向交易止损点位（修改说明：已移除反向交易功能）
input int      TakeProfit_Points         = 700;     // 止盈点位（激进优化：700点，1:10.8盈亏比，最大化盈利潜力）
input int      Max_Trades_Per_Day        = 188;     // 每天最多交易次数（修改说明：从30调整至188，满足用户需求）
input double   Max_Drawdown_Per_Day      = 20.0;    // 最大日亏限制百分比（修改说明：从8%调整至20%，满足用户需求）
input double   Max_Equity_Drawdown       = 30.0;    // 最大净值回撤百分比（新增：30%峰值回撤保护）
input bool     Manual_Reset_Drawdown     = false;   // 人工重置回撤保护（设为true可手动解除暂停）
input int      Start_Trading_Hour        = 0;       // 开始交易时间（修改说明：24小时交易，从0开始）
input int      End_Trading_Hour          = 23;      // 结束交易时间（24小时交易，到23结束）
input int      Start_Trading_Minute      = 0;       // 开始交易分钟（修改说明：24小时交易，从0分钟开始）
input int      End_Trading_Minute        = 59;      // 结束交易分钟（24小时交易，到59分钟结束）
input int      Max_Slippage              = 30;      // 最大滑点限制
input bool     Use_Reversal_Strategy     = true;     // 是否使用反转策略（修改说明：保留反转策略，但移除亏损后反向交易功能）
input bool     RSI_Filter_Enabled        = true;     // 是否启用RSI过滤
input int      RSI_Period                = 14;      // RSI周期
input double   RSI_Oversold              = 40;      // RSI超卖阈值（修改说明：从45调整至40，使用更严格的超卖阈值）
input double   RSI_Overbought            = 55;      // RSI超买下限阈值（修改说明：从50调整至55，超买区间下限）
input double   RSI_Overbought_Upper      = 85;      // RSI超买上限阈值（修改说明：新增超买上限，形成55-85超买区间）
input bool     EMA_Trend_Filter          = true;     // 是否启用EMA趋势过滤（九次优化：重新启用，测试三重过滤效果）
input int      EMA_Fast_Period           = 12;      // 快速EMA周期（十次优化：从20减至12，增加交易机会）
input int      EMA_Slow_Period           = 30;      // 慢速EMA周期（十次优化：从50减至30，增加交易机会）
input bool     Trailing_Stop_Enabled     = true;     // 是否启用移动止损（八次优化）
// 做多移动止损参数
input int      Trailing_Stop_Distance_Buy = 35;     // 做多移动止损距离（点）（修改说明：从15调整至35点，增加止损距离）
input int      Trailing_Stop_Step_Buy     = 3;      // 做多移动止损步长（点）（修改说明：做多专用参数，保持3点）
// 做空移动止损参数
input int      Trailing_Stop_Distance_Sell = 35;    // 做空移动止损距离（点）（修改说明：从15调整至35点，与做多保持一致）
input int      Trailing_Stop_Step_Sell     = 3;     // 做空移动止损步长（点）（修改说明：保持3点，与做多保持一致）
// 反转策略移动止损优化参数
// input int      Second_Stop_Profit_Points  = 35;     // 第二次止损利润保护点数（修改说明：已移除利润保护功能）
input int      Account_Report_Minutes   = 30;      // 账户报告间隔（分钟）
input double   Trading_Commission_Fixed = 10.0;  // 固定交易手续费（USD）（修改说明：每次交易固定扣除10美元手续费，更符合实际情况）

// 全局指标句柄
int rsi_handle = INVALID_HANDLE;
int ema_fast_handle = INVALID_HANDLE;
int ema_slow_handle = INVALID_HANDLE;
bool rsi_filter_active = false;  // RSI过滤是否实际激活
bool ema_filter_active = false;  // EMA过滤是否实际激活

// 全局变量
datetime last_bar_time = 0;        // 上一根K线时间
int daily_trade_count = 0;         // 当日成功交易次数
int daily_trade_attempts = 0;      // 当日交易尝试次数
double daily_start_balance = 0;    // 当日开始余额
double initial_balance = 0;        // 初始余额（回测开始时的余额）
datetime current_date = 0;         // 当前日期
datetime last_heartbeat = 0;      // 上次心跳时间

// 修改说明：新增30%峰值回撤保护相关变量
double equity_peak = 0;                    // 账户净值峰值
bool trading_paused_by_drawdown = false;   // 是否因回撤暂停交易
datetime drawdown_pause_start = 0;         // 回撤暂停开始时间
datetime last_account_report = 0;  // 上次账户报告时间
bool daily_limit_printed = false;  // 日限制提示标志
string ea_name = "XAU_VolatilitySpike_Scalper_EA_v3.18_Reversal_OptimizedStopLoss_Optimized";

// 修改说明：已移除亏损后反向交易相关变量
// bool last_trade_was_loss = false;        // 上一笔交易是否亏损（已移除）
// string last_trade_direction = "";        // 上一笔交易方向（已移除）
// datetime last_loss_time = 0;             // 上一次亏损时间（已移除）
// bool reverse_trade_pending = false;      // 是否有待执行的反向交易（已移除）
// int consecutive_reverse_count = 0;       // 连续反向交易次数（已移除）
// int max_reverse_trades = 1;              // 最大连续反向交易次数限制（已移除）
// bool is_reverse_trade = false;           // 标记当前交易是否为反转交易（已移除）

// 修改说明：新增RSI区间统计结构
struct RSIRangeStats
{
    int total_trades;           // 总交易次数
    int winning_trades;         // 盈利交易次数
    int losing_trades;          // 亏损交易次数
    double total_profit;        // 总盈利
    double total_loss;          // 总亏损
    double net_profit;          // 净盈利
    double max_profit;          // 单笔最大盈利
    double max_loss;            // 单笔最大亏损
    double avg_profit;          // 平均盈利
    double avg_loss;            // 平均亏损
    double win_rate;            // 胜率
    double profit_factor;       // 盈利因子
    double avg_rsi;             // 平均RSI值
    double min_rsi;             // 最小RSI值
    double max_rsi;             // 最大RSI值
};

// RSI区间统计数组 (0-4, 5-9, 10-14, ..., 95-99)
RSIRangeStats rsi_range_stats[20];
double current_trade_rsi = 0;   // 当前交易开仓时的RSI值

// 修改说明：新增波动值区间统计结构（115-600，每50一个区间）
struct VolatilityRangeStats
{
    int total_trades;           // 总交易次数
    int winning_trades;         // 盈利交易次数
    int losing_trades;          // 亏损交易次数
    double total_profit;        // 总盈利
    double total_loss;          // 总亏损
    double net_profit;          // 净盈利
    double max_profit;          // 单笔最大盈利
    double max_loss;            // 单笔最大亏损
    double avg_profit;          // 平均盈利
    double avg_loss;            // 平均亏损
    double win_rate;            // 胜率
    double profit_factor;       // 盈利因子
    double avg_volatility;      // 平均波动值
    double min_volatility;      // 最小波动值
    double max_volatility;      // 最大波动值
};

// 波动值区间统计数组 (115-164, 165-214, 215-264, ..., 565-614)
// 总共10个区间：[115-164], [165-214], [215-264], [265-314], [315-364], [365-414], [415-464], [465-514], [515-564], [565-614]
VolatilityRangeStats volatility_range_stats[10];
double current_trade_volatility = 0;   // 当前交易开仓时的波动值

// 修改说明：新增盈利亏损点位区间统计结构（-300到+600，每50点一个区间）
struct ProfitLossRangeStats
{
    int total_trades;           // 总交易次数

    // 做多交易的RSI统计
    int buy_trades;             // 做多交易次数
    double buy_total_rsi;       // 做多RSI值累计
    double buy_avg_rsi;         // 做多平均RSI值
    double buy_min_rsi;         // 做多最小RSI值
    double buy_max_rsi;         // 做多最大RSI值

    // 做空交易的RSI统计
    int sell_trades;            // 做空交易次数
    double sell_total_rsi;      // 做空RSI值累计
    double sell_avg_rsi;        // 做空平均RSI值
    double sell_min_rsi;        // 做空最小RSI值
    double sell_max_rsi;        // 做空最大RSI值

    // 波动值统计（不区分方向）
    double total_volatility;    // 波动值累计（用于计算平均值）
    double avg_volatility;      // 平均波动值
    double min_volatility;      // 最小波动值
    double max_volatility;      // 最大波动值

    // 盈亏统计
    double total_profit_loss;   // 总盈亏
    double avg_profit_loss;     // 平均盈亏

    string most_common_direction; // 最常见的交易方向
};

// 价格变动点位区间统计数组 (-300到+600点，每50点一个区间)
// 总共18个区间：[-300到-251], [-250到-201], ..., [-50到-1], [0到49], [50到99], ..., [550到599]
ProfitLossRangeStats profit_loss_range_stats[18];
string current_trade_direction = "";   // 当前交易方向
double current_trade_open_price = 0;   // 当前交易开仓价格

// 修改说明：新增小幅变动（±15点以内）统计结构
struct SmallMovementStats
{
    int total_trades;           // 总交易次数

    // RSI分布统计（按10点区间）
    int rsi_0_10;              // RSI 0-10
    int rsi_10_20;             // RSI 10-20
    int rsi_20_30;             // RSI 20-30
    int rsi_30_40;             // RSI 30-40
    int rsi_40_50;             // RSI 40-50
    int rsi_50_60;             // RSI 50-60
    int rsi_60_70;             // RSI 60-70
    int rsi_70_80;             // RSI 70-80
    int rsi_80_90;             // RSI 80-90
    int rsi_90_100;            // RSI 90-100

    // 波动分布统计（按50点区间）
    int vol_0_50;              // 波动 0-50点
    int vol_50_100;            // 波动 50-100点
    int vol_100_150;           // 波动 100-150点
    int vol_150_200;           // 波动 150-200点
    int vol_200_250;           // 波动 200-250点
    int vol_250_300;           // 波动 250-300点
    int vol_300_400;           // 波动 300-400点
    int vol_400_500;           // 波动 400-500点
    int vol_500_plus;          // 波动 500点以上

    // 方向统计
    int buy_trades;            // 做多交易次数
    int sell_trades;           // 做空交易次数

    // 平均值
    double avg_rsi;            // 平均RSI
    double avg_volatility;     // 平均波动
    double avg_movement;       // 平均变动点数
};

SmallMovementStats small_movement_stats;

// 修改说明：新增交易手续费统计变量
double total_commission_paid = 0.0;     // 累计支付的手续费
int commission_transactions = 0;        // 产生手续费的交易次数
double daily_commission_paid = 0.0;     // 当日支付的手续费
int commission_eroded_trades = 0;       // 被手续费侵蚀的原盈利交易次数

// 修改说明：移动止损优化相关变量（已移除利润保护功能）
// bool second_stop_applied = false;        // 是否已应用利润保护止损（已移除）
ulong current_position_ticket = 0;       // 当前持仓票号，用于跟踪止损状态

// 修改说明：新增日亏损限制统计变量
int daily_loss_limit_hit_count = 0;     // 日亏损超过限制的次数（累计）
bool daily_loss_limit_hit_today = false; // 今日是否已触发日亏损限制

// 修改说明：增强统计结构，添加做多做空分别统计
struct TradeStats
{
    int total_trades;              // 总交易次数
    int winning_trades;            // 盈利交易次数
    int losing_trades;             // 亏损交易次数
    double total_profit;           // 总盈利
    double total_loss;             // 总亏损
    double max_profit;             // 最大单笔盈利
    double max_loss;               // 最大单笔亏损
    double max_drawdown;           // 最大回撤
    double max_balance;            // 最大余额
    int consecutive_wins;          // 连续盈利次数
    int consecutive_losses;        // 连续亏损次数
    int max_consecutive_wins;      // 最大连续盈利
    int max_consecutive_losses;    // 最大连续亏损
    int volatility_signals;        // 波动信号总数
    int volume_signals;            // 成交量信号总数
    int filtered_signals;          // 被过滤的信号数
    datetime first_trade_time;     // 首次交易时间
    datetime last_trade_time;      // 最后交易时间

    // 做多统计
    int buy_total;                 // 做多总次数
    int buy_wins;                  // 做多盈利次数
    int buy_losses;                // 做多亏损次数
    double buy_profit;             // 做多总盈利
    double buy_loss;               // 做多总亏损
    double buy_max_profit;         // 做多最大盈利
    double buy_max_loss;           // 做多最大亏损

    // 做空统计
    int sell_total;                // 做空总次数
    int sell_wins;                 // 做空盈利次数
    int sell_losses;               // 做空亏损次数
    double sell_profit;            // 做空总盈利
    double sell_loss;              // 做空总亏损
    double sell_max_profit;        // 做空最大盈利
    double sell_max_loss;          // 做空最大亏损

    // 过滤统计
    int rsi_filtered;              // RSI过滤次数
    int ema_filtered;              // EMA过滤次数
    int time_filtered;             // 时间过滤次数
    int daily_limit_filtered;      // 日限制过滤次数
};

TradeStats stats;                  // 统计数据结构
datetime last_report_time = 0;     // 上次报告时间

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化EA参数验证
    if(Risk_Percent_Per_Trade <= 0 || Risk_Percent_Per_Trade > 10)
    {
        Print("错误：风险百分比必须在0-10%之间");
        return INIT_PARAMETERS_INCORRECT;
    }

    if(StopLoss_Points <= 0 || TakeProfit_Points <= 0)
    {
        Print("错误：止损止盈点位必须大于0");
        return INIT_PARAMETERS_INCORRECT;
    }

    // 修改说明：初始化统计数据结构
    ZeroMemory(stats);
    stats.max_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // 初始化日期和余额
    current_date = TimeCurrent();
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    initial_balance = AccountInfoDouble(ACCOUNT_BALANCE);  // 修改说明：记录初始余额用于正确计算总收益率
    daily_trade_count = 0;
    daily_trade_attempts = 0;
    last_report_time = TimeCurrent();

    // 修改说明：初始化30%峰值回撤保护变量
    equity_peak = AccountInfoDouble(ACCOUNT_EQUITY);
    trading_paused_by_drawdown = false;
    drawdown_pause_start = 0;

    // 修改说明：已移除反向交易相关变量初始化
    // last_trade_was_loss = false;     // 已移除
    // last_trade_direction = "";       // 已移除
    // last_loss_time = 0;              // 已移除
    // reverse_trade_pending = false;   // 已移除
    // consecutive_reverse_count = 0;   // 已移除
    // is_reverse_trade = false;        // 已移除

    // 修改说明：初始化RSI区间统计
    InitializeRSIRangeStats();
    current_trade_rsi = 0;

    // 修改说明：初始化波动值区间统计
    InitializeVolatilityRangeStats();
    current_trade_volatility = 0;

    // 修改说明：初始化盈利亏损点位区间统计
    InitializeProfitLossRangeStats();
    current_trade_direction = "";

    // 修改说明：初始化小幅变动统计
    InitializeSmallMovementStats();

    // 修改说明：初始化手续费统计变量
    total_commission_paid = 0.0;
    commission_transactions = 0;
    daily_commission_paid = 0.0;
    commission_eroded_trades = 0;

    // 修改说明：初始化移动止损优化相关变量（已移除利润保护功能）
    // second_stop_applied = false;  // 已移除
    current_position_ticket = 0;

    // 修改说明：初始化日亏损限制统计变量
    daily_loss_limit_hit_count = 0;
    daily_loss_limit_hit_today = false;

    // 修改说明：初始化RSI指标句柄
    if(RSI_Filter_Enabled)
    {
        rsi_handle = iRSI(Symbol(), PERIOD_M1, RSI_Period, PRICE_CLOSE);
        if(rsi_handle == INVALID_HANDLE)
        {
            Print("❌ RSI指标初始化失败，禁用RSI过滤");
            rsi_filter_active = false;
        }
        else
        {
            Print("✅ RSI指标初始化成功");
            rsi_filter_active = true;
        }
    }
    else
    {
        rsi_filter_active = false;
    }

    // 修改说明：初始化EMA指标句柄
    if(EMA_Trend_Filter)
    {
        ema_fast_handle = iMA(Symbol(), PERIOD_M1, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
        ema_slow_handle = iMA(Symbol(), PERIOD_M1, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);

        if(ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE)
        {
            Print("❌ EMA指标初始化失败，禁用EMA过滤");
            ema_filter_active = false;
        }
        else
        {
            Print("✅ EMA指标初始化成功");
            ema_filter_active = true;
        }
    }
    else
    {
        ema_filter_active = false;
    }

    Print("=== ", ea_name, " 初始化成功 ===");
    Print("🏆 【XAU黄金专用】波动异常反转策略EA - 移动止损优化版");
    Print("📈 经16轮优化 | 反转策略直接移动止损 | 移除利润保护，直接跟随");
    Print("🔄 策略简化：纯反转策略主逻辑 | 移除亏损后反向交易功能 | 专注主策略");
    Print("🎯 移动止损：直接按35点距离移动止损，无保本保护");
    Print("📊 新增功能：RSI区间回测统计 | 20个区间(0-4,5-9...95-99) | 详细胜率盈亏分析");
    Print("📊 新增功能：波动值区间回测统计 | 10个区间(115-164,165-214...565-614) | 每50点一个区间");
    Print("📊 新增功能：盈利亏损点位区间统计 | 18个区间(-300到+600) | 分析RSI和波动值模式");
    Print("📊 新增功能：小幅变动分析 | ±15点以内震荡 | RSI和波动分布统计");
    Print("💳 交易成本：平仓后立即扣除", DoubleToString(Trading_Commission_Fixed, 0), "美元固定手续费 | 真实成本计算");
    Print("⚡ 移动止损优先运算：每个Tick最高优先级处理移动止损，确保实时响应价格变化");
    Print("🔥 EA正在运行中，等待XAU市场信号...");
    Print("📊 当前时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("💰 当前余额：", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD");

    // 修改说明：XAU专用验证
    string current_symbol = Symbol();
    if(StringFind(current_symbol, "XAU") >= 0 || StringFind(current_symbol, "GOLD") >= 0)
    {
        Print("✅ XAU黄金品种验证通过：", current_symbol);
    }
    else
    {
        Print("⚠️ 警告：此EA专为XAU黄金优化，当前品种：", current_symbol);
        Print("⚠️ 建议使用XAUUSD等黄金品种以获得最佳效果");
    }
    Print("交易品种: ", Symbol());
    Print("当前余额: ", daily_start_balance);
    Print("风险控制: ", Risk_Percent_Per_Trade, "%");
    Print("统计报告间隔: 6小时");

    // 修改说明：参数验证，确保当前设置正确
    Print("=== 参数验证（当前优化设置）===");
    Print("波动倍数: ", Spike_Range_Multiplier, " (当前值：1.3)");
    Print("最小波动: ", Spike_Min_Points, " 点 (当前值：115)");
    Print("最大波动: ", Spike_Max_Points, " 点 (修改：600点上限，更严格避免极端波动)");
    Print("成交量倍数: ", Volume_Threshold_Mult, " (当前值：1.05)");
    Print("EMA快线: ", EMA_Fast_Period, " (当前值：12)");
    Print("EMA慢线: ", EMA_Slow_Period, " (当前值：30)");
    Print("止损: ", StopLoss_Points, " 点 (当前值：125)");
    // Print("反向交易止损: ", Reverse_StopLoss_Points, " 点 (当前值：65)");  // 已移除反向交易功能
    Print("止盈: ", TakeProfit_Points, " 点 (当前值：700)");
    Print("风险百分比: ", Risk_Percent_Per_Trade, "% (当前值：1.5%)");
    Print("交易手续费: ", Trading_Commission_Fixed, " USD (平仓后立即扣除)");
    Print("日交易限制: ", Max_Trades_Per_Day, " 次 (当前值：188)");
    Print("日亏损限制: ", Max_Drawdown_Per_Day, "% (当前值：20.0%)");
    Print("净值回撤保护: ", Max_Equity_Drawdown, "% (新增：30%峰值回撤保护)");
    Print("移动止损: 做多", Trailing_Stop_Distance_Buy, "点/", Trailing_Stop_Step_Buy, "步，做空", Trailing_Stop_Distance_Sell, "点/", Trailing_Stop_Step_Sell, "步 (修改：直接移动止损，无利润保护阶段，距离35点)");
    Print("RSI过滤: ", rsi_filter_active ? "启用" : "禁用", " (当前：启用，超卖<40，超买55-85)");
    Print("EMA过滤: ", ema_filter_active ? "启用" : "禁用", " (当前：启用)");
    Print("交易策略: ", Use_Reversal_Strategy ? "反转策略" : "顺势策略", " (当前：反转)");
    Print("策略说明: 纯反转交易策略（阳线做空，阴线做多），已移除亏损后反向交易功能");
    Print("止损设置: 统一使用", StopLoss_Points, "点止损");
    Print("移动止损: 统一的直接移动止损逻辑（35点距离直接跟随，无保本保护）");
    Print("交易时间: 24小时连续交易 (仅周一开盘后30分钟和周五闭盘前30分钟禁止)");
    Print("时间规则详情:");
    Print("  - 周一：01:30-23:59（开盘后30分钟禁止交易）");
    Print("  - 周二-周四：00:00-23:59（24小时连续交易，无限制）");
    Print("  - 周五：00:00-23:29（24小时交易，闭盘前30分钟禁止）");
    Print("  - 周末：不交易");

    // 修改说明：更新参数检查为最新优化值（已移除反向交易功能，新增固定手续费）
    if(Spike_Range_Multiplier != 1.3 || Spike_Min_Points != 115 || Spike_Max_Points != 600 || StopLoss_Points != 125 || TakeProfit_Points != 700 ||
       RSI_Oversold != 40 || RSI_Overbought != 55 || RSI_Overbought_Upper != 85 || Max_Drawdown_Per_Day != 20.0 || Max_Trades_Per_Day != 188 || Use_Reversal_Strategy != true ||
       Trading_Commission_Fixed != 10.0)
    {
        Print("❌❌❌ 警告：参数未更新！请检查参数设置！❌❌❌");
        Print("期望值：波动倍数1.3，最小波动115，最大波动600，止损125，止盈700，RSI阈值40/55-85，日交易限制188次，日亏损限制20%，反转策略，手续费10美元");
        Print("当前值：波动倍数", Spike_Range_Multiplier, "，最小波动", Spike_Min_Points, "，最大波动", Spike_Max_Points, "，止损", StopLoss_Points, "，止盈", TakeProfit_Points, "，手续费", Trading_Commission_Fixed, "美元");
        Print("RSI阈值：", RSI_Oversold, "/", RSI_Overbought, "，日交易限制：", Max_Trades_Per_Day, "次，日亏损限制：", Max_Drawdown_Per_Day, "%，策略：", Use_Reversal_Strategy ? "反转" : "顺势");
    }
    else
    {
        Print("✅✅✅ 参数验证成功：使用最新优化参数（波动倍数1.3，波动范围115-600点，止损125点，RSI阈值40/55-85，日交易限制188次，日亏损限制20%，反转策略，直接移动止损无保本保护，手续费10美元）！✅✅✅");
    }
    Print("==========================================");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== ", ea_name, " EA 停止运行 ===");
    Print("停止原因代码: ", reason);

    // 修改说明：释放指标句柄
    if(rsi_handle != INVALID_HANDLE)
    {
        IndicatorRelease(rsi_handle);
        Print("✅ RSI指标句柄已释放");
    }
    if(ema_fast_handle != INVALID_HANDLE)
    {
        IndicatorRelease(ema_fast_handle);
        Print("✅ EMA快线句柄已释放");
    }
    if(ema_slow_handle != INVALID_HANDLE)
    {
        IndicatorRelease(ema_slow_handle);
        Print("✅ EMA慢线句柄已释放");
    }

    // 修改说明：实盘模式不使用历史统计分析，直接输出当前统计
    PrintFinalReport();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 修改说明：移动止损优先运算 - 最高优先级处理移动止损
    if(Trailing_Stop_Enabled && PositionExists())
    {
        UpdateTrailingStop();
    }

    // 修改说明：每次Tick都更新统计数据
    UpdateTradeStatistics();

    // 修改说明：添加心跳功能，每5分钟输出一次状态确认EA在运行
    datetime current_time = TimeCurrent();
    if(current_time - last_heartbeat >= 300) // 5分钟 = 300秒
    {
        last_heartbeat = current_time;
        Print("💓 EA心跳检测 - 服务器时间:", TimeToString(current_time, TIME_DATE|TIME_MINUTES),
              " | 本地时间:", TimeToString(TimeLocal(), TIME_DATE|TIME_MINUTES),
              " | 余额: ", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD",
              " | 今日交易: ", daily_trade_count, " 次");
    }

    // 修改说明：定时输出详细账户报告
    if(current_time - last_account_report >= Account_Report_Minutes * 60)
    {
        last_account_report = current_time;
        PrintAccountReport();
    }

    // 修改说明：已移除反向交易检查
    // if(reverse_trade_pending && !PositionExists())  // 已移除反向交易功能
    // {
    //     ExecuteReverseTrade();
    //     return;
    // }

    // 检查是否为新的M1 K线
    if(!IsNewM1Bar()) return;

    // 修改说明：添加6小时统计报告
    CheckAndPrintPeriodicReport();

    // 修改说明：检查30%峰值回撤保护，优先级最高
    if(IsEquityDrawdownLimitReached())
    {
        stats.filtered_signals++;
        Print("🛑 跳过信号：账户净值回撤超过30%，交易已暂停");
        return;
    }

    // 检查是否已有持仓
    if(PositionExists())
    {
        Print("⏸️ 跳过信号：已有持仓");
        return;
    }

    // 检查交易时间
    if(!IsTimeToTrade())
    {
        stats.filtered_signals++;
        Print("⏰ 跳过信号：非交易时间");
        return;
    }

    // 检查日交易限制
    if(IsDailyTradeLimitReached())
    {
        stats.filtered_signals++;
        Print("🚫 跳过信号：已达日交易限制 (", daily_trade_count, "/", Max_Trades_Per_Day, ")");
        return;
    }

    // 检查日亏损限制
    if(IsDailyLossLimitReached())
    {
        stats.filtered_signals++;
        Print("💸 跳过信号：已达日亏损限制");
        return;
    }

    // 检查波动异常和成交量异常
    bool volatility_spike = IsVolatilitySpike();
    bool volume_spike = (!Volume_Spike_Enabled || IsVolumeSpike());

    // 修改说明：计算当前波动值用于区间统计
    double current_high = iHigh(Symbol(), PERIOD_M1, 1);
    double current_low = iLow(Symbol(), PERIOD_M1, 1);
    double volatility_points = (current_high - current_low) / Point();

    if(volatility_spike)
    {
        stats.volatility_signals++;
        Print("🔥 波动异常检测成功：当前波动 ", DoubleToString(volatility_points, 1), " 点");
    }
    if(volume_spike && Volume_Spike_Enabled) stats.volume_signals++;

    // 修改说明：添加信号组合状态调试
    if(!volatility_spike && volume_spike)
    {
        Print("⚠️ 仅成交量异常，缺少波动异常");
    }
    else if(volatility_spike && !volume_spike)
    {
        Print("⚠️ 仅波动异常，缺少成交量异常");
    }
    else if(!volatility_spike && !volume_spike)
    {
        static datetime last_no_signal_time = 0;
        if(TimeCurrent() - last_no_signal_time >= 300) // 每5分钟输出一次
        {
            last_no_signal_time = TimeCurrent();
            Print("📊 市场平静：无波动异常，无成交量异常");
        }
    }

    if(volatility_spike && volume_spike)
    {
        Print("🎯 双重异常信号确认：波动异常 + 成交量异常");
        string direction = GetBarDirection();
        Print("📊 K线方向：", direction);

        // 修改说明：添加RSI和EMA双重过滤，提高信号质量
        bool rsi_passed = IsRSIFilterPassed(direction);
        bool ema_passed = IsEMATrendFilterPassed(direction);

        Print("🔍 过滤器检查：RSI ", (rsi_passed ? "✅通过" : "❌未通过"),
              "，EMA ", (ema_passed ? "✅通过" : "❌未通过"));

        if(rsi_passed && ema_passed)
        {
            Print("✅ 所有过滤器通过，准备开仓");
            double lots = CalculateLotSize();

            if(lots > 0)
            {
                daily_trade_attempts++;
                // is_reverse_trade = false; // 修改说明：已移除反向交易标记

                // 修改说明：记录开仓时的RSI值和波动值用于区间统计
                RecordCurrentRSI();
                RecordCurrentVolatility(volatility_points);
                current_trade_direction = direction; // 记录交易方向

                // 记录开仓价格（用于后续计算价格变动点位）
                if(direction == "BUY")
                    current_trade_open_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                else
                    current_trade_open_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);

                OpenTrade(direction, lots);
            }
            else
            {
                stats.filtered_signals++;
                Print("❌ 手数计算失败，跳过交易 (余额不足或风险过高)");
            }
        }
        else
        {
            stats.filtered_signals++;
            bool rsi_passed = IsRSIFilterPassed(direction);
            bool ema_passed = IsEMATrendFilterPassed(direction);

            if(!rsi_passed)
            {
                stats.rsi_filtered++;
                Print("🔍 RSI过滤：", direction, " 信号被过滤");
            }
            if(!ema_passed)
            {
                stats.ema_filtered++;
                Print("🔍 EMA过滤：", direction, " 信号被过滤");
            }
        }
    }
    else
    {
        if(volatility_spike || (Volume_Spike_Enabled && volume_spike))
        {
            stats.filtered_signals++;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新的M1 K线                                              |
//+------------------------------------------------------------------+
bool IsNewM1Bar()
{
    datetime current_bar_time = iTime(Symbol(), PERIOD_M1, 0);
    
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        
        // 检查是否为新的一天，重置日计数器
        MqlDateTime dt;
        TimeToStruct(current_bar_time, dt);
        datetime today = StructToTime(dt) - dt.hour*3600 - dt.min*60 - dt.sec;
        
        if(today != current_date)
        {
            current_date = today;
            daily_trade_count = 0;
            daily_trade_attempts = 0;
            daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
            daily_limit_printed = false;  // 重置日限制提示标志
            // consecutive_reverse_count = 0;  // 修改说明：已移除反向交易计数器
            daily_loss_limit_hit_today = false;  // 修改说明：重置今日日亏损限制触发标志
            daily_commission_paid = 0.0;  // 修改说明：重置当日手续费统计

            Print("新的交易日开始，重置计数器 - 日期:", TimeToString(today, TIME_DATE));
        }
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 检查是否存在持仓                                                  |
//+------------------------------------------------------------------+
bool PositionExists()
{
    return PositionSelect(Symbol());
}

//+------------------------------------------------------------------+
//| 检查交易时间                                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeCurrent(dt);
    int current_hour = dt.hour;
    int current_minute = dt.min;
    int day_of_week = dt.day_of_week;

    // 修改说明：实现用户要求的精确交易时间控制
    // 要求：周内交易时间01:02-23:59，开盘后半小时内禁止交易，闭盘前半小时内禁止交易

    // 周末不交易（周六=6，周日=0）
    if(day_of_week == 0 || day_of_week == 6)
    {
        Print("⏰ 非交易时间：周末不交易 (周", day_of_week, ")");
        return false;
    }

    // 周一特殊处理：开盘后半小时内禁止交易（00:00-01:29禁止）
    if(day_of_week == 1)
    {
        if(current_hour == 0 || (current_hour == 1 && current_minute < 30))
        {
            Print("⏰ 非交易时间：周一开盘后半小时内禁止交易 (",
                  StringFormat("%02d:%02d", current_hour, current_minute), ")");
            return false;
        }
    }

    // 周五特殊处理：闭盘前半小时内禁止交易（23:30-23:59禁止）
    if(day_of_week == 5)
    {
        if(current_hour == 23 && current_minute >= 30)
        {
            Print("⏰ 非交易时间：周五闭盘前半小时内禁止交易 (",
                  StringFormat("%02d:%02d", current_hour, current_minute), ")");
            return false;
        }
    }

    // 修改说明：周二、周三、周四为24小时连续交易，无任何时间限制

    // 修改说明：外汇市场周内24小时连续交易，无需基础时间范围限制
    // 只需要检查开盘后30分钟和闭盘前30分钟的特殊限制即可

    // 通过所有时间检查
    return true;
}

//+------------------------------------------------------------------+
//| 检查日交易次数限制                                                |
//+------------------------------------------------------------------+
bool IsDailyTradeLimitReached()
{
    // 修改说明：只计算成功的交易次数，不计算失败的尝试
    if(daily_trade_count >= Max_Trades_Per_Day)
    {
        // 只在第一次达到限制时打印，避免重复日志
        if(!daily_limit_printed)
        {
            stats.daily_limit_filtered++;
            Print("已达到每日最大交易次数限制: ", Max_Trades_Per_Day, " (成功交易:", daily_trade_count, ", 尝试次数:", daily_trade_attempts, ")");
            daily_limit_printed = true;
        }
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查日亏损限制                                                    |
//+------------------------------------------------------------------+
bool IsDailyLossLimitReached()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double daily_loss_percent = (daily_start_balance - current_balance) / daily_start_balance * 100;

    if(daily_loss_percent >= Max_Drawdown_Per_Day)
    {
        // 修改说明：首次触发日亏损限制时增加计数
        if(!daily_loss_limit_hit_today)
        {
            daily_loss_limit_hit_count++;
            daily_loss_limit_hit_today = true;
            Print("🚨 首次触发日亏损限制: ", Max_Drawdown_Per_Day, "%, 当前亏损: ", daily_loss_percent, "% (累计触发次数:", daily_loss_limit_hit_count, ")");
        }
        else
        {
            Print("已达到每日最大亏损限制: ", Max_Drawdown_Per_Day, "%, 当前亏损: ", daily_loss_percent, "%");
        }
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查30%净值回撤限制                                              |
//+------------------------------------------------------------------+
bool IsEquityDrawdownLimitReached()
{
    // 修改说明：实现30%峰值回撤保护机制
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 检查人工重置标志
    if(Manual_Reset_Drawdown && trading_paused_by_drawdown)
    {
        trading_paused_by_drawdown = false;
        drawdown_pause_start = 0;
        equity_peak = current_equity; // 重置峰值为当前净值
        Print("🔧 人工重置回撤保护，交易恢复");
        Print("📈 重置净值峰值为：", DoubleToString(equity_peak, 2), " USD");
        Print("⚠️  请将Manual_Reset_Drawdown参数改回false");
    }

    // 更新净值峰值
    if(current_equity > equity_peak)
    {
        equity_peak = current_equity;
        // 如果净值创新高，重置暂停状态（允许人工解除后重新开始交易）
        if(trading_paused_by_drawdown)
        {
            trading_paused_by_drawdown = false;
            drawdown_pause_start = 0;
            Print("✅ 净值创新高，自动解除回撤保护暂停状态");
            Print("📈 新的净值峰值：", DoubleToString(equity_peak, 2), " USD");
        }
    }

    // 计算当前回撤百分比
    double current_drawdown_percent = 0;
    if(equity_peak > 0)
    {
        current_drawdown_percent = (equity_peak - current_equity) / equity_peak * 100;
    }

    // 检查是否触发30%回撤保护
    if(current_drawdown_percent >= Max_Equity_Drawdown && !trading_paused_by_drawdown)
    {
        trading_paused_by_drawdown = true;
        drawdown_pause_start = TimeCurrent();

        Print("🚨 警告：账户净值回撤达到", DoubleToString(current_drawdown_percent, 2), "%");
        Print("🛑 触发30%峰值回撤保护，交易已暂停");
        Print("📊 净值峰值：", DoubleToString(equity_peak, 2), " USD");
        Print("📊 当前净值：", DoubleToString(current_equity, 2), " USD");
        Print("⚠️  需要人工解除或等待净值创新高才能恢复交易");

        // 发送邮件或推送通知（如果配置了的话）
        SendNotification("EA交易暂停：净值回撤超过30%");
    }

    return trading_paused_by_drawdown;
}

//+------------------------------------------------------------------+
//| 检查波动异常                                                      |
//+------------------------------------------------------------------+
bool IsVolatilitySpike()
{
    // 获取当前M1 K线的波动范围
    double current_high = iHigh(Symbol(), PERIOD_M1, 1);
    double current_low = iLow(Symbol(), PERIOD_M1, 1);
    double current_range = current_high - current_low;
    
    // 计算过去10根K线的平均波动
    double total_range = 0;
    for(int i = 2; i <= 11; i++)
    {
        double high = iHigh(Symbol(), PERIOD_M1, i);
        double low = iLow(Symbol(), PERIOD_M1, i);
        total_range += (high - low);
    }
    double avg_range = total_range / 10.0;
    
    // 转换为点数进行比较
    double current_range_points = current_range / Point();
    double min_points_threshold = Spike_Min_Points;
    double max_points_threshold = Spike_Max_Points;

    // 修改说明：添加波动上限检查，避免极端波动下的反转失效
    if(current_range_points > max_points_threshold)
    {
        // 修改说明：添加调试信息，限制输出频率
        static datetime last_max_debug_time = 0;
        if(TimeCurrent() - last_max_debug_time >= 60) // 每分钟最多输出一次
        {
            last_max_debug_time = TimeCurrent();
            Print("⚠️ 波动过大跳过：当前波动 ", (int)current_range_points, " 点 > 上限 ", (int)max_points_threshold, " 点，避免极端市场下反转失效");
        }
        return false; // 波动过大，跳过信号
    }

    bool spike_detected = (current_range_points > min_points_threshold) &&
                         (current_range > avg_range * Spike_Range_Multiplier);
    
    // 修改说明：添加调试信息，限制输出频率
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 60) // 每分钟最多输出一次
    {
        last_debug_time = TimeCurrent();
        if(spike_detected)
        {
            Print("🔥 波动异常检测成功：当前波动 ", (int)current_range_points, " 点，平均波动 ",
                  (int)(avg_range/Point()), " 点，倍数 ", DoubleToString(current_range/avg_range, 2));
        }
        else
        {
            Print("📊 波动检测：当前 ", (int)current_range_points, " 点 (需要>",
                  (int)min_points_threshold, " 点且>",
                  (int)(avg_range/Point() * Spike_Range_Multiplier), " 点，<",
                  (int)max_points_threshold, " 点)");
        }
    }

    return spike_detected;
}

//+------------------------------------------------------------------+
//| RSI过滤检查                                                       |
//+------------------------------------------------------------------+
bool IsRSIFilterPassed(string direction)
{
    if(!rsi_filter_active || rsi_handle == INVALID_HANDLE) return true;

    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);

    // 获取RSI值
    if(CopyBuffer(rsi_handle, 0, 1, 1, rsi_buffer) <= 0)
    {
        Print("❌ RSI数据获取失败");
        return true; // 如果数据获取失败，不过滤信号
    }

    double rsi_value = rsi_buffer[0];

    // 修改说明：RSI阈值设置为50/50，使用中性的RSI过滤逻辑

    if(Use_Reversal_Strategy)
    {
        // 反转策略的RSI过滤逻辑
        if(direction == "BUY")
        {
            // 反转做多：希望RSI在超卖区域（确认超卖反弹）
            bool passed = (rsi_value < RSI_Oversold);
            if(!passed) Print("🔍 RSI过滤：反转做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (未超卖)");
            return passed;
        }
        else // SELL
        {
            // 反转做空：希望RSI在超买区域（55-85区间内确认超买回调）
            bool passed = (rsi_value >= RSI_Overbought && rsi_value <= RSI_Overbought_Upper);
            if(!passed) Print("🔍 RSI过滤：反转做空信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (不在超买区间55-85)");
            return passed;
        }
    }
    else
    {
        // 原始顺势策略的RSI过滤逻辑
        if(direction == "BUY")
        {
            // 做多时，RSI应该不在超买区域（不在55-85区间）
            bool passed = !(rsi_value >= RSI_Overbought && rsi_value <= RSI_Overbought_Upper);
            if(!passed) Print("🔍 RSI过滤：做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (在超买区间55-85)");
            return passed;
        }
        else // SELL
        {
            // 做空时，RSI应该不在超卖区域
            bool passed = (rsi_value > RSI_Oversold);
            if(!passed) Print("🔍 RSI过滤：做空信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (超卖)");
            return passed;
        }
    }
}

//+------------------------------------------------------------------+
//| EMA趋势过滤检查                                                   |
//+------------------------------------------------------------------+
bool IsEMATrendFilterPassed(string direction)
{
    if(!ema_filter_active || ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE)
        return true;

    double ema_fast[], ema_slow[];
    ArraySetAsSeries(ema_fast, true);
    ArraySetAsSeries(ema_slow, true);

    // 获取EMA值
    if(CopyBuffer(ema_fast_handle, 0, 1, 2, ema_fast) <= 0 ||
       CopyBuffer(ema_slow_handle, 0, 1, 2, ema_slow) <= 0)
    {
        Print("❌ EMA数据获取失败");
        return true;
    }

    // 当前EMA关系
    bool fast_above_slow_now = ema_fast[0] > ema_slow[0];
    bool fast_above_slow_prev = ema_fast[1] > ema_slow[1];

    if(Use_Reversal_Strategy)
    {
        // 修改说明：放宽反转策略的EMA过滤条件
        if(direction == "BUY")
        {
            // 反转做多：价格在慢线附近或快线开始向上（不要求严格穿越）
            double price = iClose(Symbol(), PERIOD_M1, 1);
            bool near_slow_ema = MathAbs(price - ema_slow[0]) / ema_slow[0] < 0.002; // 价格接近慢线（0.2%以内）
            bool fast_trending_up = ema_fast[0] > ema_fast[1]; // 快线向上

            bool condition_met = near_slow_ema || fast_trending_up || !fast_above_slow_now;
            if(!condition_met)
                Print("🔍 EMA过滤：反转做多被过滤，快线=", ema_fast[0], " 慢线=", ema_slow[0]);
            return condition_met;
        }
        else // SELL
        {
            // 反转做空：价格在慢线附近或快线开始向下
            double price = iClose(Symbol(), PERIOD_M1, 1);
            bool near_slow_ema = MathAbs(price - ema_slow[0]) / ema_slow[0] < 0.002; // 价格接近慢线（0.2%以内）
            bool fast_trending_down = ema_fast[0] < ema_fast[1]; // 快线向下

            bool condition_met = near_slow_ema || fast_trending_down || fast_above_slow_now;
            if(!condition_met)
                Print("🔍 EMA过滤：反转做空被过滤，快线=", ema_fast[0], " 慢线=", ema_slow[0]);
            return condition_met;
        }
    }
    else
    {
        // 顺势策略：跟随趋势方向
        if(direction == "BUY")
        {
            bool passed = fast_above_slow_now;
            if(!passed) Print("🔍 EMA过滤：做多被过滤，快线低于慢线");
            return passed;
        }
        else // SELL
        {
            bool passed = !fast_above_slow_now;
            if(!passed) Print("🔍 EMA过滤：做空被过滤，快线高于慢线");
            return passed;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查成交量异常                                                    |
//+------------------------------------------------------------------+
bool IsVolumeSpike()
{
    // 获取当前M1 K线的成交量
    long current_volume = iVolume(Symbol(), PERIOD_M1, 1);
    
    // 计算过去Volume_MA_Period根K线的平均成交量
    long total_volume = 0;
    for(int i = 2; i <= Volume_MA_Period + 1; i++)
    {
        total_volume += iVolume(Symbol(), PERIOD_M1, i);
    }
    double avg_volume = (double)total_volume / Volume_MA_Period;
    
    bool volume_spike = current_volume > avg_volume * Volume_Threshold_Mult;
    
    if(volume_spike)
    {
        Print("成交量异常检测成功：当前成交量 ", current_volume, "，平均成交量 ", 
              (long)avg_volume, "，倍数 ", DoubleToString(current_volume/avg_volume, 2));
    }
    
    return volume_spike;
}

//+------------------------------------------------------------------+
//| 获取K线方向                                                       |
//+------------------------------------------------------------------+
string GetBarDirection()
{
    double open_price = iOpen(Symbol(), PERIOD_M1, 1);
    double close_price = iClose(Symbol(), PERIOD_M1, 1);

    if(Use_Reversal_Strategy)
    {
        // 修改说明：反转策略 - 阳线做空，阴线做多（逆向交易）
        return (close_price > open_price) ? "SELL" : "BUY";
    }
    else
    {
        // 修改说明：顺势策略 - 阳线做多，阴线做空（跟随趋势）
        // 当前设置：主策略为顺势，但亏损后仍执行反向交易
        return (close_price > open_price) ? "BUY" : "SELL";
    }
}

//+------------------------------------------------------------------+
//| 计算交易手数                                                      |
//+------------------------------------------------------------------+
double CalculateLotSize(int custom_stop_loss = 0)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * Risk_Percent_Per_Trade / 100.0;
    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

    // 修改说明：根据交易类型使用不同的止损点位
    int actual_stop_loss = (custom_stop_loss > 0) ? custom_stop_loss : StopLoss_Points;
    double stop_loss_money = actual_stop_loss * tick_value;

    if(stop_loss_money <= 0) return 0;

    double lots = risk_amount / stop_loss_money;

    // 标准化手数
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    lots = MathMax(min_lot, MathMin(max_lot, lots));
    lots = NormalizeDouble(lots / lot_step, 0) * lot_step;

    return lots;
}

//+------------------------------------------------------------------+
//| 开仓交易                                                          |
//+------------------------------------------------------------------+
void OpenTrade(string direction, double lots)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.deviation = Max_Slippage;
    request.magic = 12345;

    // 修改说明：根据实际测试结果，强制使用IOC模式
    // IOC模式具有最佳的兼容性和执行效率
    // 避免不同品种的成交模式兼容性问题
    request.type_filling = ORDER_FILLING_IOC;

    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

    // 修改说明：统一使用标准止损点位（已移除反向交易功能）
    int actual_stop_loss = StopLoss_Points;

    // 修改说明：添加止损止盈水平检查，确保符合经纪商要求
    int stops_level = (int)SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL);
    double min_stop_distance = stops_level * point;

    if(direction == "BUY")
    {
        request.type = ORDER_TYPE_BUY;
        request.price = current_ask;

        // 确保止损止盈距离符合最小要求
        double sl_distance = MathMax(actual_stop_loss * point, min_stop_distance);
        double tp_distance = MathMax(TakeProfit_Points * point, min_stop_distance);

        request.sl = NormalizeDouble(current_ask - sl_distance, Digits());
        request.tp = NormalizeDouble(current_ask + tp_distance, Digits());
    }
    else
    {
        request.type = ORDER_TYPE_SELL;
        request.price = current_bid;

        // 确保止损止盈距离符合最小要求
        double sl_distance = MathMax(actual_stop_loss * point, min_stop_distance);
        double tp_distance = MathMax(TakeProfit_Points * point, min_stop_distance);

        request.sl = NormalizeDouble(current_bid + sl_distance, Digits());
        request.tp = NormalizeDouble(current_bid - tp_distance, Digits());
    }

    // 修改说明：快速开仓优化 - 移除开仓前的Print输出，避免延迟
    // 开仓前不输出日志，直接发送交易请求以最小化延迟

    // 发送交易请求
    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // 修改说明：只有成功交易才增加计数器和更新统计
            daily_trade_count++;
            stats.total_trades++;

            if(stats.first_trade_time == 0)
                stats.first_trade_time = TimeCurrent();
            stats.last_trade_time = TimeCurrent();

            // 修改说明：快速开仓优化 - 开仓成功后输出详细信息（不影响开仓速度）
            Print("⚡ 快速开仓成功：", direction, " ", lots, " 手 @ ", result.price);
            Print("📊 交易详情：SL=", request.sl, " (", actual_stop_loss, "点), TP=", request.tp, ", 订单号=", result.order);
            Print("📈 成交模式：", EnumToString(request.type_filling), ", 最小止损距离：", stops_level, " 点");
            Print("📊 当日统计：", daily_trade_count, "/", Max_Trades_Per_Day, " 次 (总交易:", stats.total_trades, ")");

            // 修改说明：已移除反向交易相关记录
            // last_trade_direction = direction;     // 已移除
            // last_trade_was_loss = false;          // 已移除

            // 修改说明：新开仓时重置移动止损状态变量
            // 所有交易都使用相同的直接移动止损逻辑
            current_position_ticket = result.order; // 记录当前持仓票号

            // 修改说明：等待交易完成后统计盈亏结果
            CheckTradeResult(result.order, direction, lots);
        }
        else
        {
            // 修改说明：快速开仓优化 - 开仓失败后输出详细错误信息
            Print("❌ 交易失败：错误代码 ", result.retcode, "，描述：", result.comment);
            Print("📊 失败详情：", direction, " ", lots, " 手，价格=", request.price, "，SL=", request.sl, "，TP=", request.tp);
            Print("📊 止损点位：", actual_stop_loss, " 点，成交模式：", EnumToString(request.type_filling));
        }
    }
    else
    {
        int error_code = GetLastError();
        Print("❌ 交易请求失败：错误代码 ", error_code, "，描述：", ErrorDescription(error_code));
        Print("📊 请求详情：", direction, " ", lots, " 手，价格=", request.price);
    }
}

//+------------------------------------------------------------------+
//| 获取错误描述                                                      |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
    switch(error_code)
    {
        case 4756: return "不支持的成交模式";
        case 4752: return "订单被拒绝";
        case 4753: return "订单被取消";
        case 4754: return "订单已放置";
        case 4755: return "请求已完成";
        case 10004: return "交易被禁用";
        case 10006: return "请求被拒绝";
        case 10007: return "请求被取消";
        case 10008: return "订单已放置";
        case 10009: return "请求已完成";
        case 10010: return "仅允许多头交易";
        case 10011: return "仅允许空头交易";
        case 10012: return "仅允许平仓";
        case 10013: return "仅允许减仓";
        case 10014: return "禁止开仓";
        case 10015: return "自动交易被禁用";
        case 10016: return "市场关闭";
        case 10017: return "没有足够资金";
        case 10018: return "价格已改变";
        case 10019: return "经纪商忙碌";
        case 10020: return "重新报价";
        case 10021: return "订单锁定";
        case 10022: return "买单数量无效";
        case 10023: return "卖单数量无效";
        case 10024: return "订单数量无效";
        case 10025: return "订单价格无效";
        case 10026: return "止损无效";
        case 10027: return "止盈无效";
        default: return "未知错误";
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理函数                                                  |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 修改说明：实盘模式下的交易事件处理和统计
    static double last_balance = 0;

    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // 初始化
    if(last_balance == 0)
    {
        last_balance = current_balance;
        return;
    }

    // 检查余额变化
    double balance_change = current_balance - last_balance;

    // 修改说明：计算余额变化的百分比，避免手续费等微小变化被误判为交易结果
    double balance_change_percentage = (MathAbs(balance_change) / current_balance) * 100.0;
    if(balance_change_percentage > 0.1) // 余额变化超过0.1%才认为是交易结果
    {
        // 修改说明：平仓后立即从账户余额中扣除手续费
        double commission = CalculateAndDeductCommission();

        // 立即从账户中扣除手续费（模拟真实的手续费扣除）
        double current_balance_after_commission = AccountInfoDouble(ACCOUNT_BALANCE) - commission;

        // 计算扣除手续费后的净交易结果
        double net_result_after_commission = balance_change - commission;

        Print("📊 原始交易结果：", DoubleToString(balance_change, 2), " USD");
        Print("💳 立即扣除手续费：", DoubleToString(commission, 2), " USD");
        Print("💰 最终净结果：", DoubleToString(net_result_after_commission, 2), " USD");
        Print("💼 扣费后账户余额：", DoubleToString(current_balance_after_commission, 2), " USD");

        if(net_result_after_commission > 0) // 扣除手续费后仍为盈利
        {
            stats.winning_trades++;
            stats.total_profit += net_result_after_commission; // 使用净盈利
            stats.consecutive_wins++;
            stats.consecutive_losses = 0;

            if(net_result_after_commission > stats.max_profit)
                stats.max_profit = net_result_after_commission;

            if(stats.consecutive_wins > stats.max_consecutive_wins)
                stats.max_consecutive_wins = stats.consecutive_wins;

            Print("✅ 真实盈利交易：+", DoubleToString(net_result_after_commission, 2), " USD (扣除手续费后)");

            // 修改说明：已移除反向交易相关逻辑
            // consecutive_reverse_count = 0;        // 已移除
            // reverse_trade_pending = false;        // 已移除
            // is_reverse_trade = false;             // 已移除

            // 修改说明：更新RSI区间统计（真实盈利交易）
            UpdateRSIRangeStats(current_trade_rsi, net_result_after_commission, true);

            // 修改说明：更新波动值区间统计（真实盈利交易）
            UpdateVolatilityRangeStats(current_trade_volatility, net_result_after_commission, true);

            // 修改说明：更新价格变动点位区间统计
            UpdatePriceMovementRangeStats(net_result_after_commission, current_trade_rsi, current_trade_volatility, current_trade_direction);

            // 修改说明：更新小幅变动统计（如果变动小于15点）
            UpdateSmallMovementStats(net_result_after_commission, current_trade_rsi, current_trade_volatility, current_trade_direction);
        }
        else // 扣除手续费后变为亏损或盈亏平衡
        {
            // 修改说明：原本盈利但扣除手续费后变亏损的交易，重新分类为亏损
            if(balance_change > 0)
            {
                commission_eroded_trades++; // 统计被手续费侵蚀的交易
                Print("⚠️ 手续费侵蚀：原盈利", DoubleToString(balance_change, 2), " USD，扣费后亏损", DoubleToString(net_result_after_commission, 2), " USD");
                Print("📊 累计被侵蚀交易：", commission_eroded_trades, " 笔");
            }

            stats.losing_trades++;
            stats.total_loss += MathAbs(net_result_after_commission); // 使用净亏损
            stats.consecutive_losses++;
            stats.consecutive_wins = 0;

            if(MathAbs(net_result_after_commission) > stats.max_loss)
                stats.max_loss = MathAbs(net_result_after_commission);

            if(stats.consecutive_losses > stats.max_consecutive_losses)
                stats.max_consecutive_losses = stats.consecutive_losses;

            Print("❌ 真实亏损交易：", DoubleToString(net_result_after_commission, 2), " USD (扣除手续费后)");

            // 修改说明：更新RSI区间统计（真实亏损交易）
            UpdateRSIRangeStats(current_trade_rsi, net_result_after_commission, false);

            // 修改说明：更新波动值区间统计（真实亏损交易）
            UpdateVolatilityRangeStats(current_trade_volatility, net_result_after_commission, false);

            // 修改说明：更新价格变动点位区间统计
            UpdatePriceMovementRangeStats(net_result_after_commission, current_trade_rsi, current_trade_volatility, current_trade_direction);

            // 修改说明：更新小幅变动统计（如果变动小于15点）
            UpdateSmallMovementStats(net_result_after_commission, current_trade_rsi, current_trade_volatility, current_trade_direction);
        }

        // 修改说明：所有交易结果已在上面统一处理，包括手续费扣除和重新分类

        last_balance = current_balance;
    }

    // 更新最大余额和回撤
    if(current_balance > stats.max_balance)
        stats.max_balance = current_balance;

    double current_drawdown = (stats.max_balance - current_balance) / stats.max_balance * 100;
    if(current_drawdown > stats.max_drawdown)
        stats.max_drawdown = current_drawdown;

    // 修改说明：在每笔交易完成后检查30%峰值回撤保护
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(current_equity > equity_peak)
    {
        equity_peak = current_equity;
        Print("📈 净值创新高：", DoubleToString(equity_peak, 2), " USD");
    }
}

//+------------------------------------------------------------------+
//| 检查交易结果（用于统计）                                          |
//+------------------------------------------------------------------+
void CheckTradeResult(ulong order_ticket, string direction, double lots)
{
    // 修改说明：通过定期检查持仓状态来统计交易结果，避免OnTrade()的重复统计问题
    // 这个函数暂时不实现复杂逻辑，改为在每次Tick时检查账户历史
    // 实际的盈亏统计将通过账户余额变化来计算
}

//+------------------------------------------------------------------+
//| 获取精确的交易统计（排除手续费影响）                                |
//+------------------------------------------------------------------+
void GetAccurateTradeStatistics(int &wins, int &losses, double &total_profit, double &total_loss, double &max_profit, double &max_loss)
{
    // 初始化统计变量
    wins = 0;
    losses = 0;
    total_profit = 0;
    total_loss = 0;
    max_profit = 0;
    max_loss = 0;

    // 选择历史交易
    if(!HistorySelect(0, TimeCurrent()))
        return;

    int total_deals = HistoryDealsTotal();
    if(total_deals <= 0)
        return;

    // 使用结构体来跟踪每个持仓的总盈亏
    struct PositionSummary
    {
        ulong position_id;
        ENUM_POSITION_TYPE position_type;
        double total_profit;
        bool is_processed;
    };

    PositionSummary positions[];
    ArrayResize(positions, 0);

    // 第一步：收集所有持仓的信息
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0)
            continue;

        // 只统计本EA的交易
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != 12345)
            continue;

        // 只统计本品种的交易
        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != Symbol())
            continue;

        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        ulong position_id = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

        // 跳过开仓交易（通常盈亏为0）
        if(MathAbs(deal_profit) < 0.01)
            continue;

        // 查找或创建持仓记录
        int pos_index = -1;
        for(int j = 0; j < ArraySize(positions); j++)
        {
            if(positions[j].position_id == position_id)
            {
                pos_index = j;
                break;
            }
        }

        if(pos_index == -1)
        {
            // 创建新的持仓记录
            ArrayResize(positions, ArraySize(positions) + 1);
            pos_index = ArraySize(positions) - 1;
            positions[pos_index].position_id = position_id;
            positions[pos_index].total_profit = 0;
            positions[pos_index].is_processed = false;

            // 确定持仓类型
            if(deal_type == DEAL_TYPE_SELL)
                positions[pos_index].position_type = POSITION_TYPE_BUY;
            else if(deal_type == DEAL_TYPE_BUY)
                positions[pos_index].position_type = POSITION_TYPE_SELL;
        }

        // 累加盈亏
        positions[pos_index].total_profit += deal_profit;
    }

    // 第二步：统计每个持仓的结果
    for(int i = 0; i < ArraySize(positions); i++)
    {
        double position_profit = positions[i].total_profit;

        // 只统计有意义的盈亏（排除手续费等小额变化）
        if(position_profit > 0.5) // 盈利超过0.5美元
        {
            wins++;
            total_profit += position_profit;

            if(position_profit > max_profit)
                max_profit = position_profit;
        }
        else if(position_profit < -0.5) // 亏损超过0.5美元
        {
            losses++;
            total_loss += MathAbs(position_profit);

            if(MathAbs(position_profit) > max_loss)
                max_loss = MathAbs(position_profit);
        }
        // 忽略±0.5美元内的微小变化（可能是手续费）
    }
}

//+------------------------------------------------------------------+
//| 更新交易统计（修复版本）                                          |
//+------------------------------------------------------------------+
void UpdateTradeStatistics()
{
    // 修改说明：实盘模式使用简化的实时统计更新

    // 更新最大余额和回撤
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(current_balance > stats.max_balance)
        stats.max_balance = current_balance;

    double current_drawdown = (stats.max_balance - current_balance) / stats.max_balance * 100;
    if(current_drawdown > stats.max_drawdown)
        stats.max_drawdown = current_drawdown;
}

//+------------------------------------------------------------------+
//| 修复分批止盈问题的交易统计分析                                    |
//+------------------------------------------------------------------+
void UpdateTradeStatsManually()
{
    // 修改说明：使用持仓ID来正确处理分批止盈，避免重复统计
    if(!HistorySelect(0, TimeCurrent())) return;

    int total_deals = HistoryDealsTotal();
    if(total_deals <= 0) return;

    Print("🔍 调试：开始分析 ", total_deals, " 个历史成交");

    // 重置所有统计数据
    stats.winning_trades = 0;
    stats.losing_trades = 0;
    stats.total_profit = 0;
    stats.total_loss = 0;
    stats.max_profit = 0;
    stats.max_loss = 0;
    stats.consecutive_wins = 0;
    stats.consecutive_losses = 0;
    stats.max_consecutive_wins = 0;
    stats.max_consecutive_losses = 0;

    // 重置做多做空统计
    stats.buy_total = 0;
    stats.buy_wins = 0;
    stats.buy_losses = 0;
    stats.buy_profit = 0;
    stats.buy_loss = 0;
    stats.buy_max_profit = 0;
    stats.buy_max_loss = 0;

    stats.sell_total = 0;
    stats.sell_wins = 0;
    stats.sell_losses = 0;
    stats.sell_profit = 0;
    stats.sell_loss = 0;
    stats.sell_max_profit = 0;
    stats.sell_max_loss = 0;

    // 使用结构体来跟踪每个持仓的总盈亏
    struct PositionSummary
    {
        ulong position_id;
        ENUM_POSITION_TYPE position_type;
        double total_profit;
        bool is_processed;
    };

    PositionSummary positions[];
    ArrayResize(positions, 0);

    // 第一步：收集所有持仓的信息
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0) continue;

        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != Symbol()) continue;
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != 12345) continue;

        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        ulong position_id = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

        // 跳过开仓交易（通常盈亏为0）
        if(MathAbs(deal_profit) < 0.01) continue;

        // 查找或创建持仓记录
        int pos_index = -1;
        for(int j = 0; j < ArraySize(positions); j++)
        {
            if(positions[j].position_id == position_id)
            {
                pos_index = j;
                break;
            }
        }

        if(pos_index == -1)
        {
            // 创建新的持仓记录
            ArrayResize(positions, ArraySize(positions) + 1);
            pos_index = ArraySize(positions) - 1;
            positions[pos_index].position_id = position_id;
            positions[pos_index].total_profit = 0;
            positions[pos_index].is_processed = false;

            // 确定持仓类型
            if(deal_type == DEAL_TYPE_SELL)
                positions[pos_index].position_type = POSITION_TYPE_BUY;
            else if(deal_type == DEAL_TYPE_BUY)
                positions[pos_index].position_type = POSITION_TYPE_SELL;
        }

        // 累加盈亏
        positions[pos_index].total_profit += deal_profit;
    }

    Print("🔍 调试：找到 ", ArraySize(positions), " 个独立持仓");

    // 第二步：统计每个持仓的结果
    int current_consecutive_wins = 0;
    int current_consecutive_losses = 0;

    for(int i = 0; i < ArraySize(positions); i++)
    {
        double position_profit = positions[i].total_profit;
        ENUM_POSITION_TYPE position_type = positions[i].position_type;

        // 总体统计
        double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double profit_percentage = (MathAbs(position_profit) / current_balance) * 100.0;

        if(position_profit > 0) // 盈利交易，不设置百分比限制
        {
            stats.winning_trades++;
            stats.total_profit += position_profit;
            current_consecutive_wins++;
            current_consecutive_losses = 0;

            if(position_profit > stats.max_profit)
                stats.max_profit = position_profit;

            if(current_consecutive_wins > stats.max_consecutive_wins)
                stats.max_consecutive_wins = current_consecutive_wins;
        }
        else if(position_profit < 0 && profit_percentage > 0.5) // 修改说明：亏损交易使用0.5%阈值，避免手续费等微小变化被误判
        {
            stats.losing_trades++;
            stats.total_loss += MathAbs(position_profit);
            current_consecutive_losses++;
            current_consecutive_wins = 0;

            if(MathAbs(position_profit) > stats.max_loss)
                stats.max_loss = MathAbs(position_profit);

            if(current_consecutive_losses > stats.max_consecutive_losses)
                stats.max_consecutive_losses = current_consecutive_losses;
        }

        // 做多做空分别统计
        if(position_type == POSITION_TYPE_BUY) // 做多交易
        {
            stats.buy_total++;
            if(position_profit > 0) // 盈利交易，不设置百分比限制
            {
                stats.buy_wins++;
                stats.buy_profit += position_profit;
                if(position_profit > stats.buy_max_profit)
                    stats.buy_max_profit = position_profit;
            }
            else if(position_profit < 0 && profit_percentage > 0.5) // 亏损交易使用0.5%阈值
            {
                stats.buy_losses++;
                stats.buy_loss += MathAbs(position_profit);
                if(MathAbs(position_profit) > stats.buy_max_loss)
                    stats.buy_max_loss = MathAbs(position_profit);
            }
        }
        else if(position_type == POSITION_TYPE_SELL) // 做空交易
        {
            stats.sell_total++;
            if(position_profit > 0) // 盈利交易，不设置百分比限制
            {
                stats.sell_wins++;
                stats.sell_profit += position_profit;
                if(position_profit > stats.sell_max_profit)
                    stats.sell_max_profit = position_profit;
            }
            else if(position_profit < 0 && profit_percentage > 0.5) // 亏损交易使用0.5%阈值
            {
                stats.sell_losses++;
                stats.sell_loss += MathAbs(position_profit);
                if(MathAbs(position_profit) > stats.sell_max_loss)
                    stats.sell_max_loss = MathAbs(position_profit);
            }
        }
    }

    // 更新当前连续状态
    stats.consecutive_wins = current_consecutive_wins;
    stats.consecutive_losses = current_consecutive_losses;

    // 更新总交易次数
    stats.total_trades = stats.winning_trades + stats.losing_trades;

    Print("🔍 调试：统计完成 - 做多:", stats.buy_total, " 做空:", stats.sell_total, " 总计:", stats.total_trades);
    Print("🔍 调试：盈利:", stats.winning_trades, " 亏损:", stats.losing_trades);
}

//+------------------------------------------------------------------+
//| 检查并打印周期性报告                                              |
//+------------------------------------------------------------------+
void CheckAndPrintPeriodicReport()
{
    // 修改说明：每6小时输出一次统计报告
    if(TimeCurrent() - last_report_time >= 21600) // 6小时 = 21600秒
    {
        PrintPeriodicReport();
        last_report_time = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| 打印周期性统计报告                                                |
//+------------------------------------------------------------------+
void PrintPeriodicReport()
{
    // 修改说明：实盘模式使用实时统计，不重新计算历史数据

    Print("==================== 6小时统计报告 ====================");
    Print("报告时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("当前余额：", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD");
    Print("今日交易：", daily_trade_count, "/", Max_Trades_Per_Day, " (尝试:", daily_trade_attempts, ")");

    // 修改说明：修复胜率计算，确保逻辑正确
    int completed_trades = stats.winning_trades + stats.losing_trades;
    if(completed_trades > 0)
    {
        double win_rate = (double)stats.winning_trades / completed_trades * 100;
        double avg_profit = stats.winning_trades > 0 ? stats.total_profit / stats.winning_trades : 0;
        double avg_loss = stats.losing_trades > 0 ? stats.total_loss / stats.losing_trades : 0;
        double profit_factor = stats.total_loss > 0 ? stats.total_profit / stats.total_loss : 0;

        Print("已完成交易：", completed_trades, " (开仓:", stats.total_trades, ")");
        Print("胜率：", DoubleToString(win_rate, 2), "% (", stats.winning_trades, "/", completed_trades, ")");
        Print("盈亏比：", DoubleToString(profit_factor, 2));
        Print("平均盈利：", DoubleToString(avg_profit, 2), " USD");
        Print("平均亏损：", DoubleToString(avg_loss, 2), " USD");
        Print("最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    }
    else
    {
        Print("已完成交易：0 (开仓:", stats.total_trades, ")");
        Print("胜率：0.00% (无已完成交易)");
    }

    Print("信号统计：波动信号", stats.volatility_signals, "，成交量信号", stats.volume_signals, "，过滤信号", stats.filtered_signals);
    Print("风险控制：日亏损限制触发次数", daily_loss_limit_hit_count, "次");
    Print("交易手续费：当日", DoubleToString(daily_commission_paid, 2), " USD，累计", DoubleToString(total_commission_paid, 2), " USD (", commission_transactions, "笔交易)");
    Print("手续费：", DoubleToString(Trading_Commission_Fixed, 0), " USD 每笔交易");
    Print("手续费侵蚀：", commission_eroded_trades, " 笔原盈利交易被手续费侵蚀为亏损");
    Print("====================================================");

    // 修改说明：添加RSI区间统计报告
    PrintRSIRangeReport();

    // 修改说明：添加波动值区间统计报告
    PrintVolatilityRangeReport();

    // 修改说明：添加盈利亏损点位区间统计报告
    PrintProfitLossRangeReport();

    // 修改说明：添加小幅变动统计报告
    PrintSmallMovementReport();
}

//+------------------------------------------------------------------+
//| 打印最终统计报告                                                  |
//+------------------------------------------------------------------+
void PrintFinalReport()
{
    Print("==================== XAU黄金专用EA最终报告 ====================");
    Print("回测结束时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("交易品种：", Symbol(), " (XAU黄金专用优化)");
    Print("EA版本：v3.0 - XAU VolatilitySpike Scalper (最终优化版)");
    Print("");

    // 基本统计
    Print("【基本统计】");
    Print("总开仓次数：", stats.total_trades);
    Print("盈利交易：", stats.winning_trades);
    Print("亏损交易：", stats.losing_trades);

    // 修改说明：修复胜率计算逻辑
    int completed_trades = stats.winning_trades + stats.losing_trades;
    Print("已完成交易：", completed_trades);

    if(completed_trades > 0)
    {
        double win_rate = (double)stats.winning_trades / completed_trades * 100;
        Print("胜率：", DoubleToString(win_rate, 2), "% (", stats.winning_trades, "/", completed_trades, ")");
    }
    else
    {
        Print("胜率：0.00% (无已完成交易)");
    }

    // 盈亏统计
    Print("");
    Print("【盈亏统计】");
    double net_profit = stats.total_profit - stats.total_loss;
    Print("总盈利：", DoubleToString(stats.total_profit, 2), " USD");
    Print("总亏损：", DoubleToString(stats.total_loss, 2), " USD");
    Print("净盈利：", DoubleToString(net_profit, 2), " USD");

    if(stats.total_loss > 0)
    {
        double profit_factor = stats.total_profit / stats.total_loss;
        Print("盈亏比：", DoubleToString(profit_factor, 2));
    }
    else
    {
        Print("盈亏比：", stats.total_profit > 0 ? "∞" : "0.00");
    }

    Print("最大单笔盈利：", DoubleToString(stats.max_profit, 2), " USD");
    Print("最大单笔亏损：", DoubleToString(stats.max_loss, 2), " USD");

    // 做多做空详细分析
    Print("");
    Print("【做多交易分析】");
    double buy_win_rate = 0;
    if(stats.buy_total > 0)
        buy_win_rate = (double)stats.buy_wins / stats.buy_total * 100;
    Print("做多总次数：", stats.buy_total);
    Print("做多盈利：", stats.buy_wins, " 次");
    Print("做多亏损：", stats.buy_losses, " 次");
    Print("做多胜率：", DoubleToString(buy_win_rate, 2), "% (", stats.buy_wins, "/", stats.buy_total, ")");
    Print("做多总盈利：", DoubleToString(stats.buy_profit, 2), " USD");
    Print("做多总亏损：", DoubleToString(stats.buy_loss, 2), " USD");
    Print("做多净盈利：", DoubleToString(stats.buy_profit - stats.buy_loss, 2), " USD");
    double buy_profit_factor = (stats.buy_loss > 0) ? stats.buy_profit / stats.buy_loss : 0;
    Print("做多盈亏比：", DoubleToString(buy_profit_factor, 2));
    Print("做多最大盈利：", DoubleToString(stats.buy_max_profit, 2), " USD");
    Print("做多最大亏损：", DoubleToString(stats.buy_max_loss, 2), " USD");

    Print("");
    Print("【做空交易分析】");
    double sell_win_rate = 0;
    if(stats.sell_total > 0)
        sell_win_rate = (double)stats.sell_wins / stats.sell_total * 100;
    Print("做空总次数：", stats.sell_total);
    Print("做空盈利：", stats.sell_wins, " 次");
    Print("做空亏损：", stats.sell_losses, " 次");
    Print("做空胜率：", DoubleToString(sell_win_rate, 2), "% (", stats.sell_wins, "/", stats.sell_total, ")");
    Print("做空总盈利：", DoubleToString(stats.sell_profit, 2), " USD");
    Print("做空总亏损：", DoubleToString(stats.sell_loss, 2), " USD");
    Print("做空净盈利：", DoubleToString(stats.sell_profit - stats.sell_loss, 2), " USD");
    double sell_profit_factor = (stats.sell_loss > 0) ? stats.sell_profit / stats.sell_loss : 0;
    Print("做空盈亏比：", DoubleToString(sell_profit_factor, 2));
    Print("做空最大盈利：", DoubleToString(stats.sell_max_profit, 2), " USD");
    Print("做空最大亏损：", DoubleToString(stats.sell_max_loss, 2), " USD");

    // 数据一致性验证
    Print("");
    Print("【数据一致性验证】");
    int calculated_total = stats.buy_total + stats.sell_total;
    int calculated_wins = stats.buy_wins + stats.sell_wins;
    int calculated_losses = stats.buy_losses + stats.sell_losses;
    double calculated_profit = stats.buy_profit + stats.sell_profit;
    double calculated_loss = stats.buy_loss + stats.sell_loss;

    Print("做多+做空总数：", calculated_total, " (应等于完成交易数：", completed_trades, ")");
    Print("做多+做空盈利：", calculated_wins, " (应等于总盈利：", stats.winning_trades, ")");
    Print("做多+做空亏损：", calculated_losses, " (应等于总亏损：", stats.losing_trades, ")");
    Print("做多+做空总盈利：", DoubleToString(calculated_profit, 2), " (应等于：", DoubleToString(stats.total_profit, 2), ")");
    Print("做多+做空总亏损：", DoubleToString(calculated_loss, 2), " (应等于：", DoubleToString(stats.total_loss, 2), ")");

    // 标记不一致的数据
    if(calculated_total != completed_trades)
        Print("⚠️ 警告：做多做空总数不匹配！");
    if(calculated_wins != stats.winning_trades)
        Print("⚠️ 警告：盈利交易数不匹配！");
    if(calculated_losses != stats.losing_trades)
        Print("⚠️ 警告：亏损交易数不匹配！");
    if(MathAbs(calculated_profit - stats.total_profit) > 0.01)
        Print("⚠️ 警告：总盈利金额不匹配！");
    if(MathAbs(calculated_loss - stats.total_loss) > 0.01)
        Print("⚠️ 警告：总亏损金额不匹配！");

    // 连续交易统计
    Print("");
    Print("【连续交易统计】");
    Print("最大连续盈利：", stats.max_consecutive_wins, " 次");
    Print("最大连续亏损：", stats.max_consecutive_losses, " 次");
    Print("当前连续盈利：", stats.consecutive_wins, " 次");
    Print("当前连续亏损：", stats.consecutive_losses, " 次");

    // 风险统计
    Print("");
    Print("【风险统计】");
    Print("最大余额：", DoubleToString(stats.max_balance, 2), " USD");
    Print("最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    Print("当前余额：", DoubleToString(current_balance, 2), " USD");
    Print("日亏损限制触发次数：", daily_loss_limit_hit_count, " 次");
    // Print("连续反向交易限制：最多", max_reverse_trades, "次，当前", consecutive_reverse_count, "次 (过滤≤0.5%小幅亏损)");  // 已移除反向交易功能

    // 修改说明：添加手续费统计汇总
    Print("====================================================");
    Print("💳 交易手续费统计汇总");
    Print("手续费：", DoubleToString(Trading_Commission_Fixed, 0), " USD 每笔交易");
    Print("累计手续费：", DoubleToString(total_commission_paid, 2), " USD");
    Print("手续费交易笔数：", commission_transactions, " 笔");
    Print("手续费侵蚀交易：", commission_eroded_trades, " 笔 (原盈利变亏损)");
    if(commission_transactions > 0)
    {
        Print("平均每笔手续费：", DoubleToString(total_commission_paid / commission_transactions, 2), " USD");
        double erosion_rate = (double)commission_eroded_trades / commission_transactions * 100.0;
        Print("手续费侵蚀率：", DoubleToString(erosion_rate, 1), "% (被侵蚀交易/总交易)");
    }

    // 计算手续费对总收益的影响
    if(initial_balance > 0)
    {
        double commission_impact_percentage = (total_commission_paid / initial_balance) * 100.0;
        Print("手续费占初始资金比例：", DoubleToString(commission_impact_percentage, 2), "%");

        double net_return_after_commission = ((current_balance - total_commission_paid - initial_balance) / initial_balance) * 100.0;
        Print("扣除手续费后净收益率：", DoubleToString(net_return_after_commission, 2), "%");
    }

    // 修改说明：修复总收益率计算，使用初始余额而不是当日开始余额
    if(initial_balance > 0)
    {
        double total_return = (current_balance - initial_balance) / initial_balance * 100;
        Print("总收益率：", DoubleToString(total_return, 2), "%");
        Print("初始余额：", DoubleToString(initial_balance, 2), " USD");
    }

    // 信号统计
    Print("");
    Print("【信号统计】");
    Print("波动异常信号：", stats.volatility_signals);
    Print("成交量异常信号：", stats.volume_signals);
    Print("被过滤信号：", stats.filtered_signals);
    // 修改说明：使用精确统计计算信号转换率（排除手续费影响）
    int accurate_wins = 0, accurate_losses = 0;
    double temp_profit = 0, temp_loss = 0, temp_max_profit = 0, temp_max_loss = 0;
    GetAccurateTradeStatistics(accurate_wins, accurate_losses, temp_profit, temp_loss, temp_max_profit, temp_max_loss);

    int total_trades = accurate_wins + accurate_losses;
    int total_signals = total_trades + stats.filtered_signals;
    if(total_signals > 0)
    {
        double signal_efficiency = (double)total_trades / total_signals * 100;
        Print("信号转换率：", DoubleToString(signal_efficiency, 2), "% (", total_trades, "/", total_signals, ") [排除手续费统计]");
    }

    Print("");
    Print("【过滤效果分析】");
    if(stats.rsi_filtered > 0)
        Print("RSI过滤次数：", stats.rsi_filtered);
    if(stats.ema_filtered > 0)
        Print("EMA过滤次数：", stats.ema_filtered);
    if(stats.time_filtered > 0)
        Print("时间过滤次数：", stats.time_filtered);
    if(stats.daily_limit_filtered > 0)
        Print("日限制过滤次数：", stats.daily_limit_filtered);

    // 策略效果分析
    Print("");
    Print("【策略效果分析】");
    if(stats.buy_total > 0 && stats.sell_total > 0)
    {
        Print("做多做空比例：", DoubleToString((double)stats.buy_total/stats.sell_total, 2), ":1");
        Print("做多贡献度：", DoubleToString((stats.buy_profit - stats.buy_loss)/(stats.total_profit - stats.total_loss)*100, 2), "%");
        Print("做空贡献度：", DoubleToString((stats.sell_profit - stats.sell_loss)/(stats.total_profit - stats.total_loss)*100, 2), "%");
    }

    // 数学期望分析
    if(completed_trades > 0)
    {
        double win_rate_decimal = (double)stats.winning_trades / completed_trades;
        double profit_factor = (stats.total_loss > 0) ? stats.total_profit / stats.total_loss : 0;
        double mathematical_expectation = win_rate_decimal * profit_factor * 100;
        Print("数学期望：", DoubleToString(mathematical_expectation, 2), "%");

        if(mathematical_expectation > 100)
            Print("策略评级：优秀 (数学期望>100%)");
        else if(mathematical_expectation > 80)
            Print("策略评级：良好 (数学期望>80%)");
        else if(mathematical_expectation > 60)
            Print("策略评级：一般 (数学期望>60%)");
        else
            Print("策略评级：需要改进 (数学期望<60%)");
    }

    // 时间统计
    Print("");
    Print("【时间统计】");
    if(stats.first_trade_time > 0)
    {
        Print("首次交易：", TimeToString(stats.first_trade_time, TIME_DATE|TIME_MINUTES));
        Print("最后交易：", TimeToString(stats.last_trade_time, TIME_DATE|TIME_MINUTES));

        int trading_days = (int)((stats.last_trade_time - stats.first_trade_time) / 86400) + 1;
        if(trading_days > 0)
        {
            double trades_per_day = (double)stats.total_trades / trading_days;
            Print("交易天数：", trading_days, " 天");
            Print("日均交易：", DoubleToString(trades_per_day, 2), " 次");
        }
    }
    else
    {
        Print("无交易记录");
    }

    // 参数设置
    Print("");
    Print("【参数设置】");
    Print("风险百分比：", Risk_Percent_Per_Trade, "% (优化值：1.5%)");
    Print("波动倍数：", Spike_Range_Multiplier, " (优化值：1.3)");
    Print("波动范围：", Spike_Min_Points, "-", Spike_Max_Points, " 点 (优化值：115-600点，更严格避免极端波动)");
    Print("成交量过滤：", Volume_Spike_Enabled ? "启用" : "禁用", " (优化：启用)");
    Print("成交量倍数：", Volume_Threshold_Mult, " (优化值：1.05)");
    Print("止损：", StopLoss_Points, " 点 (优化值：125点)");
    Print("止盈：", TakeProfit_Points, " 点 (优化值：300点)");
    Print("移动止损：统一", Trailing_Stop_Distance_Buy, "点/", Trailing_Stop_Step_Buy, "步 (做多做空相同，无保本保护)");
    Print("RSI过滤：", RSI_Filter_Enabled ? "启用" : "禁用", " (优化：启用)");
    Print("EMA过滤：", EMA_Trend_Filter ? "启用" : "禁用", " (优化：启用)");
    Print("交易策略：", Use_Reversal_Strategy ? "反转策略" : "顺势策略", " (优化：反转)");
    Print("日交易限制：", Max_Trades_Per_Day, " 次 (设置：30次)");
    Print("日亏损限制：", Max_Drawdown_Per_Day, "% (设置：2.5%)");

    Print("====================================================");

    // 修改说明：在最终报告中添加RSI区间统计
    PrintRSIRangeReport();

    // 修改说明：在最终报告中添加波动值区间统计
    PrintVolatilityRangeReport();

    // 修改说明：在最终报告中添加盈利亏损点位区间统计
    PrintProfitLossRangeReport();

    // 修改说明：在最终报告中添加小幅变动统计
    PrintSmallMovementReport();

    Print("回测报告生成完成");
    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 定时账户报告                                                      |
//+------------------------------------------------------------------+
void PrintAccountReport()
{
    datetime current_time = TimeCurrent();
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double current_margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);

    // 计算当日盈亏
    double daily_pnl = current_balance - daily_start_balance;
    double daily_pnl_percent = (daily_start_balance > 0) ? (daily_pnl / daily_start_balance * 100) : 0;

    // 计算总收益
    double total_return = (initial_balance > 0) ? ((current_balance - initial_balance) / initial_balance * 100) : 0;

    // 计算当前回撤
    double current_drawdown = (stats.max_balance > 0) ? ((stats.max_balance - current_balance) / stats.max_balance * 100) : 0;

    Print("==================== 账户状态报告 ====================");
    Print("📅 服务器时间：", TimeToString(current_time, TIME_DATE|TIME_MINUTES));
    Print("📅 本地时间：", TimeToString(TimeLocal(), TIME_DATE|TIME_MINUTES));
    Print("💰 当前余额：", DoubleToString(current_balance, 2), " USD");
    Print("💎 当前净值：", DoubleToString(current_equity, 2), " USD");
    Print("📊 初始余额：", DoubleToString(initial_balance, 2), " USD");
    Print("📈 总收益率：", DoubleToString(total_return, 2), "%");
    Print("📉 最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    Print("📉 当前回撤：", DoubleToString(current_drawdown, 2), "%");

    // 修改说明：添加30%峰值回撤保护状态显示
    double equity_drawdown_percent = (equity_peak > 0) ? ((equity_peak - current_equity) / equity_peak * 100) : 0;
    Print("🛡️  净值峰值：", DoubleToString(equity_peak, 2), " USD");
    Print("🛡️  峰值回撤：", DoubleToString(equity_drawdown_percent, 2), "% / ", Max_Equity_Drawdown, "%");
    if(trading_paused_by_drawdown)
    {
        Print("🚨 回撤保护状态：已暂停交易（回撤超过30%）");
        if(drawdown_pause_start > 0)
            Print("⏰ 暂停开始时间：", TimeToString(drawdown_pause_start, TIME_DATE|TIME_MINUTES));
    }
    else
    {
        Print("✅ 回撤保护状态：正常交易");
    }

    Print("🔄 当日盈亏：", DoubleToString(daily_pnl, 2), " USD (", DoubleToString(daily_pnl_percent, 2), "%)");
    Print("🎯 今日交易：", daily_trade_count, " 次 / ", Max_Trades_Per_Day, " 次限制");

    // 保证金信息
    Print("💳 已用保证金：", DoubleToString(current_margin, 2), " USD");
    Print("💳 可用保证金：", DoubleToString(free_margin, 2), " USD");
    if(margin_level > 0)
        Print("💳 保证金水平：", DoubleToString(margin_level, 2), "%");

    // 持仓信息
    int total_positions = PositionsTotal();
    if(total_positions > 0)
    {
        Print("📍 当前持仓：", total_positions, " 个");
        for(int i = 0; i < total_positions; i++)
        {
            if(PositionGetTicket(i) > 0 && PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                string pos_type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "做多" : "做空";
                double pos_volume = PositionGetDouble(POSITION_VOLUME);
                double pos_profit = PositionGetDouble(POSITION_PROFIT);
                Print("   ", pos_type, " ", DoubleToString(pos_volume, 2), " 手，浮盈：", DoubleToString(pos_profit, 2), " USD");
            }
        }
    }
    else
    {
        Print("📍 当前持仓：无");
    }

    // 策略表现统计（修改说明：使用交易历史精确统计，排除手续费影响）
    Print("================== 策略表现统计 ==================");

    // 获取精确的交易统计（排除手续费）
    int accurate_wins = 0;
    int accurate_losses = 0;
    double accurate_total_profit = 0;
    double accurate_total_loss = 0;
    double accurate_max_profit = 0;
    double accurate_max_loss = 0;

    GetAccurateTradeStatistics(accurate_wins, accurate_losses, accurate_total_profit, accurate_total_loss, accurate_max_profit, accurate_max_loss);

    int total_completed = accurate_wins + accurate_losses;
    if(total_completed > 0)
    {
        double win_rate = (double)accurate_wins / total_completed * 100;
        double profit_factor = (accurate_total_loss > 0) ? (accurate_total_profit / accurate_total_loss) : 0;
        double avg_win = (accurate_wins > 0) ? (accurate_total_profit / accurate_wins) : 0;
        double avg_loss = (accurate_losses > 0) ? (accurate_total_loss / accurate_losses) : 0;
        double expectancy = win_rate * 2.17; // 使用理论盈亏比计算数学期望

        Print("📊 总交易次数：", total_completed, " 次（排除手续费统计）");
        Print("✅ 盈利交易：", accurate_wins, " 次");
        Print("❌ 亏损交易：", accurate_losses, " 次");
        Print("🎯 当前胜率：", DoubleToString(win_rate, 2), "%");
        Print("💰 总盈利：", DoubleToString(accurate_total_profit, 2), " USD");
        Print("💸 总亏损：", DoubleToString(accurate_total_loss, 2), " USD");
        Print("📈 盈亏比：", DoubleToString(profit_factor, 2));
        Print("💵 平均盈利：", DoubleToString(avg_win, 2), " USD");
        Print("💸 平均亏损：", DoubleToString(avg_loss, 2), " USD");
        Print("🧮 数学期望：", DoubleToString(expectancy, 2), "%");
        Print("🏆 最大盈利：", DoubleToString(stats.max_profit, 2), " USD");
        Print("💔 最大亏损：", DoubleToString(stats.max_loss, 2), " USD");
        Print("🔥 连续盈利：", stats.consecutive_wins, " 次 (最大:", stats.max_consecutive_wins, ")");
        Print("❄️ 连续亏损：", stats.consecutive_losses, " 次 (最大:", stats.max_consecutive_losses, ")");

        // 策略评级
        string rating = "未知";
        if(expectancy > 120) rating = "优秀";
        else if(expectancy > 100) rating = "良好";
        else if(expectancy > 80) rating = "一般";
        else rating = "需要改进";

        Print("⭐ 策略评级：", rating, " (期望值:", DoubleToString(expectancy, 2), "%)");
    }
    else
    {
        Print("📊 暂无交易记录");
    }

    // 信号统计
    Print("================== 信号统计分析 ==================");
    Print("📡 波动异常信号：", stats.volatility_signals, " 次");
    Print("📊 成交量异常信号：", stats.volume_signals, " 次");
    Print("🚫 被过滤信号：", stats.filtered_signals, " 次");

    int total_signals = total_completed + stats.filtered_signals;
    if(total_signals > 0)
    {
        double signal_efficiency = (double)total_completed / total_signals * 100;
        Print("🎯 信号转换率：", DoubleToString(signal_efficiency, 2), "% (", total_completed, "/", total_signals, ")");
    }

    Print("🔍 RSI过滤次数：", stats.rsi_filtered, " 次");
    Print("📈 EMA过滤次数：", stats.ema_filtered, " 次");

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 移动止损更新                                                      |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    // 修改说明：移动止损优先运算 - 快速检查持仓状态
    if(!PositionSelect(Symbol())) return;

    // 修改说明：优先获取关键价格信息，确保实时性
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

    // 获取持仓信息
    double position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);
    double position_sl = PositionGetDouble(POSITION_SL);
    double position_tp = PositionGetDouble(POSITION_TP);
    ulong position_ticket = PositionGetInteger(POSITION_TICKET);
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    // 修改说明：检查是否为新持仓，如果是则重置移动止损状态
    if(position_ticket != current_position_ticket)
    {
        current_position_ticket = position_ticket;
        // 修改说明：所有交易都使用相同的直接移动止损逻辑
        Print("🔄 新持仓检测：开始移动止损跟踪 (票号:", position_ticket, ")");
    }

    double new_sl = 0;
    bool should_modify = false;

    if(position_type == POSITION_TYPE_BUY) // 做多持仓
    {
        // 修改说明：做多纯粹移动止损，无保本保护
        new_sl = current_bid - Trailing_Stop_Distance_Buy * point;

        // 检查是否需要移动止损（只能向有利方向移动）
        if(new_sl > position_sl + Trailing_Stop_Step_Buy * point)
        {
            should_modify = true;
            // 修改说明：移动止损优化 - 减少Print频率，避免每次移动都输出日志
            static datetime last_buy_log = 0;
            if(TimeCurrent() - last_buy_log >= 5) // 每5秒最多输出一次
            {
                Print("🛡️ 做多移动止损：新止损 ", new_sl, " (距离", Trailing_Stop_Distance_Buy, "点)");
                last_buy_log = TimeCurrent();
            }
        }
    }
    else if(position_type == POSITION_TYPE_SELL) // 做空持仓
    {
        // 修改说明：做空纯粹移动止损，无保本保护
        new_sl = current_ask + Trailing_Stop_Distance_Sell * point;

        // 检查是否需要移动止损（只能向有利方向移动）
        if(new_sl < position_sl - Trailing_Stop_Step_Sell * point)
        {
            should_modify = true;
            // 修改说明：移动止损优化 - 减少Print频率，避免每次移动都输出日志
            static datetime last_sell_log = 0;
            if(TimeCurrent() - last_sell_log >= 5) // 每5秒最多输出一次
            {
                Print("🛡️ 做空移动止损：新止损 ", new_sl, " (距离", Trailing_Stop_Distance_Sell, "点)");
                last_sell_log = TimeCurrent();
            }
        }
    }

    if(should_modify)
    {
        MqlTradeRequest request = {};
        MqlTradeResult result = {};

        request.action = TRADE_ACTION_SLTP;
        request.position = position_ticket;
        request.sl = NormalizeDouble(new_sl, Digits());
        request.tp = position_tp;

        if(OrderSend(request, result))
        {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
                // 修改说明：显示具体使用的移动止损参数和利润保护状态
                string direction_text = (position_type == POSITION_TYPE_BUY) ? "做多" : "做空";
                int used_distance = (position_type == POSITION_TYPE_BUY) ? Trailing_Stop_Distance_Buy : Trailing_Stop_Distance_Sell;

                // 修改说明：移动止损优化 - 减少成功日志频率
                static datetime last_success_log = 0;
                if(TimeCurrent() - last_success_log >= 10) // 每10秒最多输出一次成功日志
                {
                    Print("✅ ", direction_text, "移动止损成功：新止损 ", new_sl, "，开仓价 ", position_open_price, "，距离 ", used_distance, " 点");
                    last_success_log = TimeCurrent();
                }
            }
            else
            {
                Print("❌ 移动止损失败：错误代码 ", result.retcode);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 执行反向交易（已移除功能）                                          |
//+------------------------------------------------------------------+
// void ExecuteReverseTrade()  // 已移除反向交易功能
// {
//     // 修改说明：反向交易功能已完全移除
//     return;
// }

//+------------------------------------------------------------------+
//| 初始化RSI区间统计                                                 |
//+------------------------------------------------------------------+
void InitializeRSIRangeStats()
{
    for(int i = 0; i < 20; i++)
    {
        rsi_range_stats[i].total_trades = 0;
        rsi_range_stats[i].winning_trades = 0;
        rsi_range_stats[i].losing_trades = 0;
        rsi_range_stats[i].total_profit = 0.0;
        rsi_range_stats[i].total_loss = 0.0;
        rsi_range_stats[i].net_profit = 0.0;
        rsi_range_stats[i].max_profit = 0.0;
        rsi_range_stats[i].max_loss = 0.0;
        rsi_range_stats[i].avg_profit = 0.0;
        rsi_range_stats[i].avg_loss = 0.0;
        rsi_range_stats[i].win_rate = 0.0;
        rsi_range_stats[i].profit_factor = 0.0;
        rsi_range_stats[i].avg_rsi = 0.0;
        rsi_range_stats[i].min_rsi = 100.0;
        rsi_range_stats[i].max_rsi = 0.0;
    }
    Print("📊 RSI区间统计已初始化 (20个区间，每区间5个RSI值)");
}

//+------------------------------------------------------------------+
//| 初始化波动值区间统计                                               |
//+------------------------------------------------------------------+
void InitializeVolatilityRangeStats()
{
    for(int i = 0; i < 10; i++)
    {
        volatility_range_stats[i].total_trades = 0;
        volatility_range_stats[i].winning_trades = 0;
        volatility_range_stats[i].losing_trades = 0;
        volatility_range_stats[i].total_profit = 0.0;
        volatility_range_stats[i].total_loss = 0.0;
        volatility_range_stats[i].net_profit = 0.0;
        volatility_range_stats[i].max_profit = 0.0;
        volatility_range_stats[i].max_loss = 0.0;
        volatility_range_stats[i].avg_profit = 0.0;
        volatility_range_stats[i].avg_loss = 0.0;
        volatility_range_stats[i].win_rate = 0.0;
        volatility_range_stats[i].profit_factor = 0.0;
        volatility_range_stats[i].avg_volatility = 0.0;
        volatility_range_stats[i].min_volatility = 999999.0;
        volatility_range_stats[i].max_volatility = 0.0;
    }
    Print("📊 波动值区间统计已初始化 (10个区间，115-600点，每50点一个区间)");
}

//+------------------------------------------------------------------+
//| 初始化盈利亏损点位区间统计                                         |
//+------------------------------------------------------------------+
void InitializeProfitLossRangeStats()
{
    for(int i = 0; i < 18; i++)
    {
        profit_loss_range_stats[i].total_trades = 0;

        // 做多RSI统计初始化
        profit_loss_range_stats[i].buy_trades = 0;
        profit_loss_range_stats[i].buy_total_rsi = 0.0;
        profit_loss_range_stats[i].buy_avg_rsi = 0.0;
        profit_loss_range_stats[i].buy_min_rsi = 100.0;
        profit_loss_range_stats[i].buy_max_rsi = 0.0;

        // 做空RSI统计初始化
        profit_loss_range_stats[i].sell_trades = 0;
        profit_loss_range_stats[i].sell_total_rsi = 0.0;
        profit_loss_range_stats[i].sell_avg_rsi = 0.0;
        profit_loss_range_stats[i].sell_min_rsi = 100.0;
        profit_loss_range_stats[i].sell_max_rsi = 0.0;

        // 波动值统计初始化
        profit_loss_range_stats[i].total_volatility = 0.0;
        profit_loss_range_stats[i].avg_volatility = 0.0;
        profit_loss_range_stats[i].min_volatility = 999999.0;
        profit_loss_range_stats[i].max_volatility = 0.0;

        // 盈亏统计初始化
        profit_loss_range_stats[i].total_profit_loss = 0.0;
        profit_loss_range_stats[i].avg_profit_loss = 0.0;
        profit_loss_range_stats[i].most_common_direction = "";
    }
    Print("📊 盈利亏损点位区间统计已初始化 (18个区间，-300到+600美元，区分做多做空RSI)");
}

//+------------------------------------------------------------------+
//| 初始化小幅变动统计                                                 |
//+------------------------------------------------------------------+
void InitializeSmallMovementStats()
{
    small_movement_stats.total_trades = 0;

    // RSI分布初始化
    small_movement_stats.rsi_0_10 = 0;
    small_movement_stats.rsi_10_20 = 0;
    small_movement_stats.rsi_20_30 = 0;
    small_movement_stats.rsi_30_40 = 0;
    small_movement_stats.rsi_40_50 = 0;
    small_movement_stats.rsi_50_60 = 0;
    small_movement_stats.rsi_60_70 = 0;
    small_movement_stats.rsi_70_80 = 0;
    small_movement_stats.rsi_80_90 = 0;
    small_movement_stats.rsi_90_100 = 0;

    // 波动分布初始化
    small_movement_stats.vol_0_50 = 0;
    small_movement_stats.vol_50_100 = 0;
    small_movement_stats.vol_100_150 = 0;
    small_movement_stats.vol_150_200 = 0;
    small_movement_stats.vol_200_250 = 0;
    small_movement_stats.vol_250_300 = 0;
    small_movement_stats.vol_300_400 = 0;
    small_movement_stats.vol_400_500 = 0;
    small_movement_stats.vol_500_plus = 0;

    // 方向统计初始化
    small_movement_stats.buy_trades = 0;
    small_movement_stats.sell_trades = 0;

    // 平均值初始化
    small_movement_stats.avg_rsi = 0.0;
    small_movement_stats.avg_volatility = 0.0;
    small_movement_stats.avg_movement = 0.0;

    Print("📊 小幅变动统计已初始化 (±15点以内的价格变动)");
}

//+------------------------------------------------------------------+
//| 更新小幅变动统计                                                   |
//+------------------------------------------------------------------+
void UpdateSmallMovementStats(double profit_loss_usd, double rsi_value, double volatility_value, string direction)
{
    // 修改说明：使用真实交易数据计算价格变动点位（与UpdatePriceMovementRangeStats相同的逻辑）
    double real_price_movement_points = 0;
    bool found_real_data = false;

    // 获取最后一笔已完成的交易
    if(HistorySelect(TimeCurrent() - 86400, TimeCurrent()))
    {
        int total_deals = HistoryDealsTotal();
        if(total_deals >= 2)
        {
            ulong last_deal_ticket = HistoryDealGetTicket(total_deals - 1);
            ulong prev_deal_ticket = HistoryDealGetTicket(total_deals - 2);

            if(last_deal_ticket > 0 && prev_deal_ticket > 0)
            {
                double close_price = HistoryDealGetDouble(last_deal_ticket, DEAL_PRICE);
                double open_price = HistoryDealGetDouble(prev_deal_ticket, DEAL_PRICE);
                ENUM_DEAL_TYPE close_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(last_deal_ticket, DEAL_TYPE);
                ENUM_DEAL_TYPE open_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(prev_deal_ticket, DEAL_TYPE);

                if((open_type == DEAL_TYPE_BUY && close_type == DEAL_TYPE_SELL) ||
                   (open_type == DEAL_TYPE_SELL && close_type == DEAL_TYPE_BUY))
                {
                    if(open_type == DEAL_TYPE_BUY)
                    {
                        real_price_movement_points = (close_price - open_price) / Point();
                    }
                    else
                    {
                        real_price_movement_points = (open_price - close_price) / Point();
                    }
                    found_real_data = true;
                }
            }
        }
    }

    double price_movement_points = 0;
    if(found_real_data)
    {
        price_movement_points = real_price_movement_points;
    }
    else
    {
        double point_value = 1.0;
        price_movement_points = profit_loss_usd / point_value;
    }

    // 只统计变动幅度小于15点的交易
    if(MathAbs(price_movement_points) >= 15.0)
    {
        return; // 变动幅度大于等于15点，不统计
    }

    // 更新基本统计
    small_movement_stats.total_trades++;

    // 更新平均值
    double total_rsi = small_movement_stats.avg_rsi * (small_movement_stats.total_trades - 1) + rsi_value;
    small_movement_stats.avg_rsi = total_rsi / small_movement_stats.total_trades;

    double total_vol = small_movement_stats.avg_volatility * (small_movement_stats.total_trades - 1) + volatility_value;
    small_movement_stats.avg_volatility = total_vol / small_movement_stats.total_trades;

    double total_movement = small_movement_stats.avg_movement * (small_movement_stats.total_trades - 1) + price_movement_points;
    small_movement_stats.avg_movement = total_movement / small_movement_stats.total_trades;

    // 更新RSI分布统计
    if(rsi_value >= 0 && rsi_value < 10) small_movement_stats.rsi_0_10++;
    else if(rsi_value >= 10 && rsi_value < 20) small_movement_stats.rsi_10_20++;
    else if(rsi_value >= 20 && rsi_value < 30) small_movement_stats.rsi_20_30++;
    else if(rsi_value >= 30 && rsi_value < 40) small_movement_stats.rsi_30_40++;
    else if(rsi_value >= 40 && rsi_value < 50) small_movement_stats.rsi_40_50++;
    else if(rsi_value >= 50 && rsi_value < 60) small_movement_stats.rsi_50_60++;
    else if(rsi_value >= 60 && rsi_value < 70) small_movement_stats.rsi_60_70++;
    else if(rsi_value >= 70 && rsi_value < 80) small_movement_stats.rsi_70_80++;
    else if(rsi_value >= 80 && rsi_value < 90) small_movement_stats.rsi_80_90++;
    else if(rsi_value >= 90 && rsi_value <= 100) small_movement_stats.rsi_90_100++;

    // 更新波动分布统计
    if(volatility_value >= 0 && volatility_value < 50) small_movement_stats.vol_0_50++;
    else if(volatility_value >= 50 && volatility_value < 100) small_movement_stats.vol_50_100++;
    else if(volatility_value >= 100 && volatility_value < 150) small_movement_stats.vol_100_150++;
    else if(volatility_value >= 150 && volatility_value < 200) small_movement_stats.vol_150_200++;
    else if(volatility_value >= 200 && volatility_value < 250) small_movement_stats.vol_200_250++;
    else if(volatility_value >= 250 && volatility_value < 300) small_movement_stats.vol_250_300++;
    else if(volatility_value >= 300 && volatility_value < 400) small_movement_stats.vol_300_400++;
    else if(volatility_value >= 400 && volatility_value < 500) small_movement_stats.vol_400_500++;
    else if(volatility_value >= 500) small_movement_stats.vol_500_plus++;

    // 更新方向统计
    if(direction == "BUY")
        small_movement_stats.buy_trades++;
    else if(direction == "SELL")
        small_movement_stats.sell_trades++;

    string data_source_small = found_real_data ? "真实交易" : "估算";
    Print("📊 小幅变动记录：", DoubleToString(price_movement_points, 1), "点 (", data_source_small, ") | RSI=", DoubleToString(rsi_value, 1),
          " | 波动=", DoubleToString(volatility_value, 0), "点 | 方向=", direction);
}

//+------------------------------------------------------------------+
//| 打印小幅变动统计报告                                               |
//+------------------------------------------------------------------+
void PrintSmallMovementReport()
{
    Print("====================================================");
    Print("📊 小幅变动分析报告 (±15点以内)");
    Print("====================================================");
    Print("目标：分析价格变动小于15点的交易中RSI和波动的分布特征");
    Print("用途：找出什么条件下容易出现小幅震荡，避免无效交易");
    Print("----------------------------------------------------");

    if(small_movement_stats.total_trades == 0)
    {
        Print("📊 暂无小幅变动交易数据");
        Print("====================================================");
        return;
    }

    Print("📈 基本统计：");
    Print("总交易次数：", small_movement_stats.total_trades, " 笔");
    Print("做多交易：", small_movement_stats.buy_trades, " 笔 (",
          DoubleToString(small_movement_stats.buy_trades * 100.0 / small_movement_stats.total_trades, 1), "%)");
    Print("做空交易：", small_movement_stats.sell_trades, " 笔 (",
          DoubleToString(small_movement_stats.sell_trades * 100.0 / small_movement_stats.total_trades, 1), "%)");
    Print("平均RSI：", DoubleToString(small_movement_stats.avg_rsi, 1));
    Print("平均波动：", DoubleToString(small_movement_stats.avg_volatility, 0), " 点");
    Print("平均变动：", DoubleToString(small_movement_stats.avg_movement, 1), " 点");

    Print("----------------------------------------------------");
    Print("📊 RSI分布分析：");

    // RSI分布
    struct RSIDistribution {
        string range;
        int count;
        double percentage;
    };

    RSIDistribution rsi_dist[10];
    rsi_dist[0].range = "0-10";   rsi_dist[0].count = small_movement_stats.rsi_0_10;
    rsi_dist[1].range = "10-20";  rsi_dist[1].count = small_movement_stats.rsi_10_20;
    rsi_dist[2].range = "20-30";  rsi_dist[2].count = small_movement_stats.rsi_20_30;
    rsi_dist[3].range = "30-40";  rsi_dist[3].count = small_movement_stats.rsi_30_40;
    rsi_dist[4].range = "40-50";  rsi_dist[4].count = small_movement_stats.rsi_40_50;
    rsi_dist[5].range = "50-60";  rsi_dist[5].count = small_movement_stats.rsi_50_60;
    rsi_dist[6].range = "60-70";  rsi_dist[6].count = small_movement_stats.rsi_60_70;
    rsi_dist[7].range = "70-80";  rsi_dist[7].count = small_movement_stats.rsi_70_80;
    rsi_dist[8].range = "80-90";  rsi_dist[8].count = small_movement_stats.rsi_80_90;
    rsi_dist[9].range = "90-100"; rsi_dist[9].count = small_movement_stats.rsi_90_100;

    for(int i = 0; i < 10; i++)
    {
        rsi_dist[i].percentage = rsi_dist[i].count * 100.0 / small_movement_stats.total_trades;
        if(rsi_dist[i].count > 0)
        {
            Print("RSI ", rsi_dist[i].range, "：", rsi_dist[i].count, " 笔 (",
                  DoubleToString(rsi_dist[i].percentage, 1), "%)");
        }
    }

    Print("----------------------------------------------------");
    Print("📊 波动分布分析：");

    // 波动分布
    struct VolatilityDistribution {
        string range;
        int count;
        double percentage;
    };

    VolatilityDistribution vol_dist[9];
    vol_dist[0].range = "0-50";     vol_dist[0].count = small_movement_stats.vol_0_50;
    vol_dist[1].range = "50-100";   vol_dist[1].count = small_movement_stats.vol_50_100;
    vol_dist[2].range = "100-150";  vol_dist[2].count = small_movement_stats.vol_100_150;
    vol_dist[3].range = "150-200";  vol_dist[3].count = small_movement_stats.vol_150_200;
    vol_dist[4].range = "200-250";  vol_dist[4].count = small_movement_stats.vol_200_250;
    vol_dist[5].range = "250-300";  vol_dist[5].count = small_movement_stats.vol_250_300;
    vol_dist[6].range = "300-400";  vol_dist[6].count = small_movement_stats.vol_300_400;
    vol_dist[7].range = "400-500";  vol_dist[7].count = small_movement_stats.vol_400_500;
    vol_dist[8].range = "500+";     vol_dist[8].count = small_movement_stats.vol_500_plus;

    for(int i = 0; i < 9; i++)
    {
        vol_dist[i].percentage = vol_dist[i].count * 100.0 / small_movement_stats.total_trades;
        if(vol_dist[i].count > 0)
        {
            Print("波动 ", vol_dist[i].range, " 点：", vol_dist[i].count, " 笔 (",
                  DoubleToString(vol_dist[i].percentage, 1), "%)");
        }
    }

    Print("----------------------------------------------------");
    Print("💡 小幅变动特征分析：");

    // 找出最集中的RSI区间
    int max_rsi_count = 0;
    string max_rsi_range = "";
    for(int i = 0; i < 10; i++)
    {
        if(rsi_dist[i].count > max_rsi_count)
        {
            max_rsi_count = rsi_dist[i].count;
            max_rsi_range = rsi_dist[i].range;
        }
    }

    // 找出最集中的波动区间
    int max_vol_count = 0;
    string max_vol_range = "";
    for(int i = 0; i < 9; i++)
    {
        if(vol_dist[i].count > max_vol_count)
        {
            max_vol_count = vol_dist[i].count;
            max_vol_range = vol_dist[i].range;
        }
    }

    if(max_rsi_count > 0)
    {
        Print("🎯 最容易小幅震荡的RSI区间：", max_rsi_range, " (", max_rsi_count, "笔，",
              DoubleToString(max_rsi_count * 100.0 / small_movement_stats.total_trades, 1), "%)");
    }

    if(max_vol_count > 0)
    {
        Print("🎯 最容易小幅震荡的波动区间：", max_vol_range, " 点 (", max_vol_count, "笔，",
              DoubleToString(max_vol_count * 100.0 / small_movement_stats.total_trades, 1), "%)");
    }

    Print("⚠️ 建议：在RSI ", max_rsi_range, " 且波动 ", max_vol_range, " 点时谨慎交易，容易出现15点以内小幅震荡");

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 记录当前RSI值                                                     |
//+------------------------------------------------------------------+
void RecordCurrentRSI()
{
    if(rsi_handle == INVALID_HANDLE)
    {
        current_trade_rsi = 50.0; // 默认值
        return;
    }

    double rsi_buffer[1];
    if(CopyBuffer(rsi_handle, 0, 1, 1, rsi_buffer) <= 0)
    {
        current_trade_rsi = 50.0; // 默认值
        Print("⚠️ 无法获取RSI值，使用默认值50");
        return;
    }

    current_trade_rsi = rsi_buffer[0];
    Print("📊 记录开仓RSI值：", DoubleToString(current_trade_rsi, 2));
}

//+------------------------------------------------------------------+
//| 记录当前波动值                                                     |
//+------------------------------------------------------------------+
void RecordCurrentVolatility(double volatility_value)
{
    current_trade_volatility = volatility_value;
    Print("📊 记录开仓波动值：", DoubleToString(current_trade_volatility, 1), " 点");
}

//+------------------------------------------------------------------+
//| 更新价格变动点位区间统计                                           |
//+------------------------------------------------------------------+
void UpdatePriceMovementRangeStats(double profit_loss_usd, double rsi_value, double volatility_value, string direction)
{
    // 修改说明：使用真实交易数据计算价格变动点位
    // 从交易历史中获取最后一笔交易的真实开仓价和平仓价
    double real_price_movement_points = 0;
    bool found_real_data = false;

    // 获取最后一笔已完成的交易
    if(HistorySelect(TimeCurrent() - 86400, TimeCurrent())) // 查看最近24小时的交易历史
    {
        int total_deals = HistoryDealsTotal();
        if(total_deals >= 2) // 至少需要开仓和平仓两笔交易
        {
            // 获取最后两笔交易（应该是一对开仓和平仓）
            ulong last_deal_ticket = HistoryDealGetTicket(total_deals - 1); // 最后一笔（平仓）
            ulong prev_deal_ticket = HistoryDealGetTicket(total_deals - 2); // 倒数第二笔（开仓）

            if(last_deal_ticket > 0 && prev_deal_ticket > 0)
            {
                // 获取交易详情
                double close_price = HistoryDealGetDouble(last_deal_ticket, DEAL_PRICE);
                double open_price = HistoryDealGetDouble(prev_deal_ticket, DEAL_PRICE);
                ENUM_DEAL_TYPE close_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(last_deal_ticket, DEAL_TYPE);
                ENUM_DEAL_TYPE open_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(prev_deal_ticket, DEAL_TYPE);

                // 确保是一对开仓和平仓交易
                if((open_type == DEAL_TYPE_BUY && close_type == DEAL_TYPE_SELL) ||
                   (open_type == DEAL_TYPE_SELL && close_type == DEAL_TYPE_BUY))
                {
                    // 计算真实的价格变动点位
                    if(open_type == DEAL_TYPE_BUY) // 做多交易
                    {
                        real_price_movement_points = (close_price - open_price) / Point();
                    }
                    else // 做空交易
                    {
                        real_price_movement_points = (open_price - close_price) / Point();
                    }
                    found_real_data = true;
                }
            }
        }
    }

    // 如果无法获取真实交易数据，使用估算方法
    double price_movement_points = 0;
    if(found_real_data)
    {
        price_movement_points = real_price_movement_points;
    }
    else
    {
        // 备用方案：根据盈亏和品种特性估算价格变动
        // 对于XAUUSD，大约每点价值1美元（需要根据实际合约大小调整）
        double point_value = 1.0; // 可以根据实际情况调整
        price_movement_points = profit_loss_usd / point_value;
    }

    // 确定价格变动点位区间索引 (-300到+600点，每50点一个区间)
    // 区间：[-300到-251]=0, [-250到-201]=1, ..., [-50到-1]=5, [0到49]=6, [50到99]=7, ..., [550到599]=17
    int range_index = -1;

    if(price_movement_points >= -300 && price_movement_points <= -251) range_index = 0;
    else if(price_movement_points >= -250 && price_movement_points <= -201) range_index = 1;
    else if(price_movement_points >= -200 && price_movement_points <= -151) range_index = 2;
    else if(price_movement_points >= -150 && price_movement_points <= -101) range_index = 3;
    else if(price_movement_points >= -100 && price_movement_points <= -51) range_index = 4;
    else if(price_movement_points >= -50 && price_movement_points <= -1) range_index = 5;
    else if(price_movement_points >= 0 && price_movement_points <= 49) range_index = 6;
    else if(price_movement_points >= 50 && price_movement_points <= 99) range_index = 7;
    else if(price_movement_points >= 100 && price_movement_points <= 149) range_index = 8;
    else if(price_movement_points >= 150 && price_movement_points <= 199) range_index = 9;
    else if(price_movement_points >= 200 && price_movement_points <= 249) range_index = 10;
    else if(price_movement_points >= 250 && price_movement_points <= 299) range_index = 11;
    else if(price_movement_points >= 300 && price_movement_points <= 349) range_index = 12;
    else if(price_movement_points >= 350 && price_movement_points <= 399) range_index = 13;
    else if(price_movement_points >= 400 && price_movement_points <= 449) range_index = 14;
    else if(price_movement_points >= 450 && price_movement_points <= 499) range_index = 15;
    else if(price_movement_points >= 500 && price_movement_points <= 549) range_index = 16;
    else if(price_movement_points >= 550 && price_movement_points <= 599) range_index = 17;
    else
    {
        Print("⚠️ 价格变动", DoubleToString(price_movement_points, 1), "点超出统计范围-300到+600");
        return; // 超出范围，不统计
    }

    // 更新基本统计
    profit_loss_range_stats[range_index].total_trades++;
    profit_loss_range_stats[range_index].total_profit_loss += price_movement_points;
    profit_loss_range_stats[range_index].avg_profit_loss = profit_loss_range_stats[range_index].total_profit_loss / profit_loss_range_stats[range_index].total_trades;

    // 更新交易方向和对应的RSI统计
    if(direction == "BUY")
    {
        profit_loss_range_stats[range_index].buy_trades++;
        profit_loss_range_stats[range_index].buy_total_rsi += rsi_value;
        profit_loss_range_stats[range_index].buy_avg_rsi = profit_loss_range_stats[range_index].buy_total_rsi / profit_loss_range_stats[range_index].buy_trades;

        if(rsi_value < profit_loss_range_stats[range_index].buy_min_rsi)
            profit_loss_range_stats[range_index].buy_min_rsi = rsi_value;
        if(rsi_value > profit_loss_range_stats[range_index].buy_max_rsi)
            profit_loss_range_stats[range_index].buy_max_rsi = rsi_value;
    }
    else if(direction == "SELL")
    {
        profit_loss_range_stats[range_index].sell_trades++;
        profit_loss_range_stats[range_index].sell_total_rsi += rsi_value;
        profit_loss_range_stats[range_index].sell_avg_rsi = profit_loss_range_stats[range_index].sell_total_rsi / profit_loss_range_stats[range_index].sell_trades;

        if(rsi_value < profit_loss_range_stats[range_index].sell_min_rsi)
            profit_loss_range_stats[range_index].sell_min_rsi = rsi_value;
        if(rsi_value > profit_loss_range_stats[range_index].sell_max_rsi)
            profit_loss_range_stats[range_index].sell_max_rsi = rsi_value;
    }

    // 确定最常见的交易方向
    if(profit_loss_range_stats[range_index].buy_trades > profit_loss_range_stats[range_index].sell_trades)
        profit_loss_range_stats[range_index].most_common_direction = "BUY";
    else if(profit_loss_range_stats[range_index].sell_trades > profit_loss_range_stats[range_index].buy_trades)
        profit_loss_range_stats[range_index].most_common_direction = "SELL";
    else
        profit_loss_range_stats[range_index].most_common_direction = "EQUAL";

    // 更新波动值统计
    profit_loss_range_stats[range_index].total_volatility += volatility_value;
    profit_loss_range_stats[range_index].avg_volatility = profit_loss_range_stats[range_index].total_volatility / profit_loss_range_stats[range_index].total_trades;

    if(volatility_value < profit_loss_range_stats[range_index].min_volatility)
        profit_loss_range_stats[range_index].min_volatility = volatility_value;
    if(volatility_value > profit_loss_range_stats[range_index].max_volatility)
        profit_loss_range_stats[range_index].max_volatility = volatility_value;

    int range_start = -300 + range_index * 50;
    int range_end = range_start + 49;

    string movement_desc = (price_movement_points >= 0) ?
        StringFormat("上涨%.1f点", price_movement_points) :
        StringFormat("下跌%.1f点", MathAbs(price_movement_points));

    string data_source = found_real_data ? "真实交易" : "估算";

    Print("📊 价格变动统计：", movement_desc, " (", data_source, ") | RSI=", DoubleToString(rsi_value, 1),
          " | 波动=", DoubleToString(volatility_value, 0), "点 | 方向=", direction,
          " | 盈亏=", DoubleToString(profit_loss_usd, 1), "美元");
}

//+------------------------------------------------------------------+
//| 打印盈利亏损点位区间统计报告                                       |
//+------------------------------------------------------------------+
void PrintProfitLossRangeReport()
{
    Print("====================================================");
    Print("📊 价格变动分析 - 找出最佳交易条件");
    Print("====================================================");
    Print("目标：分析不同价格变动幅度对应的RSI和波动值，找出筛选条件");
    Print("----------------------------------------------------");

    // 创建一个按盈利排序的数组
    struct ProfitRangeData {
        int range_index;        // 原始区间索引
        double avg_profit;
        int trades;
        double avg_rsi;
        double avg_volatility;
        string range_desc;
    };

    ProfitRangeData sorted_ranges[18];
    int valid_ranges = 0;

    // 收集有效数据
    for(int i = 0; i < 18; i++)
    {
        if(profit_loss_range_stats[i].total_trades == 0) continue;

        int range_start = -300 + i * 50;
        int range_end = range_start + 49;

        sorted_ranges[valid_ranges].range_index = i;
        sorted_ranges[valid_ranges].avg_profit = profit_loss_range_stats[i].avg_profit_loss;
        sorted_ranges[valid_ranges].trades = profit_loss_range_stats[i].total_trades;
        // 计算综合RSI（加权平均）
        double total_rsi = profit_loss_range_stats[i].buy_total_rsi + profit_loss_range_stats[i].sell_total_rsi;
        sorted_ranges[valid_ranges].avg_rsi = total_rsi / profit_loss_range_stats[i].total_trades;
        sorted_ranges[valid_ranges].avg_volatility = profit_loss_range_stats[i].avg_volatility;

        if(range_start >= 0)
            sorted_ranges[valid_ranges].range_desc = StringFormat("上涨%d~%d点", range_start, range_end);
        else
            sorted_ranges[valid_ranges].range_desc = StringFormat("下跌%d~%d点", MathAbs(range_end), MathAbs(range_start));

        valid_ranges++;
    }

    // 简单排序（按平均盈利从高到低）
    for(int i = 0; i < valid_ranges - 1; i++)
    {
        for(int j = i + 1; j < valid_ranges; j++)
        {
            if(sorted_ranges[i].avg_profit < sorted_ranges[j].avg_profit)
            {
                ProfitRangeData temp = sorted_ranges[i];
                sorted_ranges[i] = sorted_ranges[j];
                sorted_ranges[j] = temp;
            }
        }
    }

    Print("🏆 价格变动排行榜 (从最大涨幅到最大跌幅)：");
    Print("----------------------------------------------------");

    for(int i = 0; i < valid_ranges; i++)
    {
        string movement_desc = (sorted_ranges[i].avg_profit >= 0) ?
            StringFormat("📈 平均上涨%.1f点", sorted_ranges[i].avg_profit) :
            StringFormat("📉 平均下跌%.1f点", MathAbs(sorted_ranges[i].avg_profit));

        Print("第", (i+1), "名：", sorted_ranges[i].range_desc, " (", sorted_ranges[i].trades, "笔)");
        Print("    ", movement_desc);

        // 获取原始数据以显示详细的做多做空RSI
        int orig_index = sorted_ranges[i].range_index;

        Print("    🎯 关键条件：波动=", DoubleToString(sorted_ranges[i].avg_volatility, 0), "点");

        // 分别显示做多和做空的RSI条件
        if(profit_loss_range_stats[orig_index].buy_trades > 0)
        {
            Print("    📈 做多条件：RSI=", DoubleToString(profit_loss_range_stats[orig_index].buy_avg_rsi, 1),
                  " (", profit_loss_range_stats[orig_index].buy_trades, "笔，范围",
                  DoubleToString(profit_loss_range_stats[orig_index].buy_min_rsi, 1), "~",
                  DoubleToString(profit_loss_range_stats[orig_index].buy_max_rsi, 1), ")");
        }

        if(profit_loss_range_stats[orig_index].sell_trades > 0)
        {
            Print("    📉 做空条件：RSI=", DoubleToString(profit_loss_range_stats[orig_index].sell_avg_rsi, 1),
                  " (", profit_loss_range_stats[orig_index].sell_trades, "笔，范围",
                  DoubleToString(profit_loss_range_stats[orig_index].sell_min_rsi, 1), "~",
                  DoubleToString(profit_loss_range_stats[orig_index].sell_max_rsi, 1), ")");
        }

        // 给出具体建议
        if(i < 3) // 前3名
        {
            Print("    ✅ 建议：");
            if(profit_loss_range_stats[orig_index].buy_trades > 0)
            {
                Print("      做多：RSI在", DoubleToString(profit_loss_range_stats[orig_index].buy_avg_rsi-3, 0), "~",
                      DoubleToString(profit_loss_range_stats[orig_index].buy_avg_rsi+3, 0), "且波动>",
                      DoubleToString(sorted_ranges[i].avg_volatility-20, 0), "点时");
            }
            if(profit_loss_range_stats[orig_index].sell_trades > 0)
            {
                Print("      做空：RSI在", DoubleToString(profit_loss_range_stats[orig_index].sell_avg_rsi-3, 0), "~",
                      DoubleToString(profit_loss_range_stats[orig_index].sell_avg_rsi+3, 0), "且波动>",
                      DoubleToString(sorted_ranges[i].avg_volatility-20, 0), "点时");
            }
        }
        else if(i >= valid_ranges - 3) // 后3名
        {
            Print("    ❌ 警告：避免以下条件");
            if(profit_loss_range_stats[orig_index].buy_trades > 0)
            {
                Print("      做多：RSI在", DoubleToString(profit_loss_range_stats[orig_index].buy_avg_rsi-3, 0), "~",
                      DoubleToString(profit_loss_range_stats[orig_index].buy_avg_rsi+3, 0), "且波动",
                      DoubleToString(sorted_ranges[i].avg_volatility-20, 0), "~",
                      DoubleToString(sorted_ranges[i].avg_volatility+20, 0), "点时");
            }
            if(profit_loss_range_stats[orig_index].sell_trades > 0)
            {
                Print("      做空：RSI在", DoubleToString(profit_loss_range_stats[orig_index].sell_avg_rsi-3, 0), "~",
                      DoubleToString(profit_loss_range_stats[orig_index].sell_avg_rsi+3, 0), "且波动",
                      DoubleToString(sorted_ranges[i].avg_volatility-20, 0), "~",
                      DoubleToString(sorted_ranges[i].avg_volatility+20, 0), "点时");
            }
        }
        Print("    ----------------------------------------");
    }

    Print("====================================================");
    Print("🎯 筛选条件建议：");

    if(valid_ranges >= 6)
    {
        // 分析前1/3和后1/3的差异，区分做多做空
        int top_third = valid_ranges / 3;

        double top_buy_rsi_sum = 0, top_sell_rsi_sum = 0, top_vol_sum = 0;
        int top_buy_count = 0, top_sell_count = 0;

        double bottom_buy_rsi_sum = 0, bottom_sell_rsi_sum = 0, bottom_vol_sum = 0;
        int bottom_buy_count = 0, bottom_sell_count = 0;

        // 统计最赚钱的1/3
        for(int i = 0; i < top_third; i++)
        {
            int orig_idx = sorted_ranges[i].range_index;
            if(profit_loss_range_stats[orig_idx].buy_trades > 0)
            {
                top_buy_rsi_sum += profit_loss_range_stats[orig_idx].buy_avg_rsi;
                top_buy_count++;
            }
            if(profit_loss_range_stats[orig_idx].sell_trades > 0)
            {
                top_sell_rsi_sum += profit_loss_range_stats[orig_idx].sell_avg_rsi;
                top_sell_count++;
            }
            top_vol_sum += sorted_ranges[i].avg_volatility;
        }

        // 统计最亏钱的1/3
        for(int i = valid_ranges - top_third; i < valid_ranges; i++)
        {
            int orig_idx = sorted_ranges[i].range_index;
            if(profit_loss_range_stats[orig_idx].buy_trades > 0)
            {
                bottom_buy_rsi_sum += profit_loss_range_stats[orig_idx].buy_avg_rsi;
                bottom_buy_count++;
            }
            if(profit_loss_range_stats[orig_idx].sell_trades > 0)
            {
                bottom_sell_rsi_sum += profit_loss_range_stats[orig_idx].sell_avg_rsi;
                bottom_sell_count++;
            }
            bottom_vol_sum += sorted_ranges[i].avg_volatility;
        }

        Print("💡 最赚钱的交易条件：");
        if(top_buy_count > 0)
            Print("   做多：RSI平均", DoubleToString(top_buy_rsi_sum/top_buy_count, 1));
        if(top_sell_count > 0)
            Print("   做空：RSI平均", DoubleToString(top_sell_rsi_sum/top_sell_count, 1));
        Print("   波动：平均", DoubleToString(top_vol_sum/top_third, 0), "点");

        Print("💡 最亏钱的交易条件：");
        if(bottom_buy_count > 0)
            Print("   做多：RSI平均", DoubleToString(bottom_buy_rsi_sum/bottom_buy_count, 1));
        if(bottom_sell_count > 0)
            Print("   做空：RSI平均", DoubleToString(bottom_sell_rsi_sum/bottom_sell_count, 1));
        Print("   波动：平均", DoubleToString(bottom_vol_sum/top_third, 0), "点");

        Print("🔧 参数优化建议：");

        // 做多RSI建议
        if(top_buy_count > 0 && bottom_buy_count > 0)
        {
            double top_buy_avg = top_buy_rsi_sum/top_buy_count;
            double bottom_buy_avg = bottom_buy_rsi_sum/bottom_buy_count;
            if(top_buy_avg < bottom_buy_avg)
                Print("   - 做多RSI过滤：建议RSI<", DoubleToString((top_buy_avg + bottom_buy_avg)/2, 0), "时做多");
            else
                Print("   - 做多RSI过滤：建议RSI>", DoubleToString((top_buy_avg + bottom_buy_avg)/2, 0), "时做多");
        }

        // 做空RSI建议
        if(top_sell_count > 0 && bottom_sell_count > 0)
        {
            double top_sell_avg = top_sell_rsi_sum/top_sell_count;
            double bottom_sell_avg = bottom_sell_rsi_sum/bottom_sell_count;
            if(top_sell_avg < bottom_sell_avg)
                Print("   - 做空RSI过滤：建议RSI<", DoubleToString((top_sell_avg + bottom_sell_avg)/2, 0), "时做空");
            else
                Print("   - 做空RSI过滤：建议RSI>", DoubleToString((top_sell_avg + bottom_sell_avg)/2, 0), "时做空");
        }

        // 波动建议
        double top_avg_vol = top_vol_sum / top_third;
        double bottom_avg_vol = bottom_vol_sum / top_third;
        if(top_avg_vol > bottom_avg_vol)
            Print("   - 波动过滤：建议只在波动>", DoubleToString((top_avg_vol + bottom_avg_vol)/2, 0), "点时交易");
        else
            Print("   - 波动过滤：建议只在波动<", DoubleToString((top_avg_vol + bottom_avg_vol)/2, 0), "点时交易");
    }

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 分析盈利亏损点位区间                                               |
//+------------------------------------------------------------------+
void AnalyzeProfitLossRanges()
{
    Print("🏆 交易结果深度分析");
    Print("====================================================");

    // 找出交易最多的区间
    int max_trades = 0;
    int most_active_range = -1;

    // 找出平均盈利最高的区间
    double best_avg_profit = -999999;
    int best_profit_range = -1;

    // 找出平均亏损最大的区间
    double worst_avg_loss = 999999;
    int worst_loss_range = -1;

    int min_trades_threshold = 2; // 最少交易次数阈值

    for(int i = 0; i < 18; i++)
    {
        if(profit_loss_range_stats[i].total_trades < min_trades_threshold) continue;

        // 最活跃区间
        if(profit_loss_range_stats[i].total_trades > max_trades)
        {
            max_trades = profit_loss_range_stats[i].total_trades;
            most_active_range = i;
        }

        // 最佳平均盈利区间
        if(profit_loss_range_stats[i].avg_profit_loss > best_avg_profit)
        {
            best_avg_profit = profit_loss_range_stats[i].avg_profit_loss;
            best_profit_range = i;
        }

        // 最差平均亏损区间
        if(profit_loss_range_stats[i].avg_profit_loss < worst_avg_loss)
        {
            worst_avg_loss = profit_loss_range_stats[i].avg_profit_loss;
            worst_loss_range = i;
        }
    }

    // 输出分析结果
    if(most_active_range >= 0)
    {
        int start = -300 + most_active_range * 50;
        int end = start + 49;
        string range_desc = (start < 0) ? StringFormat("亏损%d~%d美元区间", MathAbs(end), MathAbs(start)) : StringFormat("盈利%d~%d美元区间", start, end);
        Print("📈 最常见结果：", range_desc, " (", max_trades, "笔交易)");
        // 计算综合RSI平均值
        double combined_rsi = 0;
        if(profit_loss_range_stats[most_active_range].total_trades > 0)
        {
            combined_rsi = (profit_loss_range_stats[most_active_range].buy_total_rsi + profit_loss_range_stats[most_active_range].sell_total_rsi) / profit_loss_range_stats[most_active_range].total_trades;
        }
        Print("    这类交易的特征：综合RSI平均", DoubleToString(combined_rsi, 1),
              "，波动平均", DoubleToString(profit_loss_range_stats[most_active_range].avg_volatility, 0), "点");
        Print("    交易方向：做多", profit_loss_range_stats[most_active_range].buy_trades,
              "笔，做空", profit_loss_range_stats[most_active_range].sell_trades, "笔");
    }

    if(best_profit_range >= 0)
    {
        int start = -300 + best_profit_range * 50;
        int end = start + 49;
        Print("💰 最佳盈利表现：平均每笔赚", DoubleToString(best_avg_profit, 1), "美元");
        // 计算最佳盈利区间的综合RSI平均值
        double best_combined_rsi = 0;
        if(profit_loss_range_stats[best_profit_range].total_trades > 0)
        {
            best_combined_rsi = (profit_loss_range_stats[best_profit_range].buy_total_rsi + profit_loss_range_stats[best_profit_range].sell_total_rsi) / profit_loss_range_stats[best_profit_range].total_trades;
        }
        Print("    最赚钱交易的特征：综合RSI平均", DoubleToString(best_combined_rsi, 1),
              "，波动平均", DoubleToString(profit_loss_range_stats[best_profit_range].avg_volatility, 0), "点");
        Print("    建议：寻找类似RSI和波动条件的交易机会");
    }

    if(worst_loss_range >= 0)
    {
        int start = -300 + worst_loss_range * 50;
        int end = start + 49;
        Print("📉 最差亏损表现：平均每笔亏", DoubleToString(MathAbs(worst_avg_loss), 1), "美元");
        // 计算最差亏损区间的综合RSI平均值
        double worst_combined_rsi = 0;
        if(profit_loss_range_stats[worst_loss_range].total_trades > 0)
        {
            worst_combined_rsi = (profit_loss_range_stats[worst_loss_range].buy_total_rsi + profit_loss_range_stats[worst_loss_range].sell_total_rsi) / profit_loss_range_stats[worst_loss_range].total_trades;
        }
        Print("    最亏钱交易的特征：综合RSI平均", DoubleToString(worst_combined_rsi, 1),
              "，波动平均", DoubleToString(profit_loss_range_stats[worst_loss_range].avg_volatility, 0), "点");
        Print("    建议：避免在类似RSI和波动条件下交易");
    }

    // RSI和波动值模式分析
    Print("----------------------------------------------------");
    Print("📋 盈利vs亏损的市场条件对比：");

    // 分析盈利区间的RSI和波动特征
    double profit_rsi_sum = 0, profit_vol_sum = 0;
    int profit_count = 0, profit_trades = 0;
    double loss_rsi_sum = 0, loss_vol_sum = 0;
    int loss_count = 0, loss_trades = 0;

    for(int i = 0; i < 18; i++)
    {
        if(profit_loss_range_stats[i].total_trades < min_trades_threshold) continue;

        if(profit_loss_range_stats[i].avg_profit_loss > 0) // 盈利区间
        {
            // 计算该区间的综合RSI（做多和做空的加权平均）
            double combined_rsi = (profit_loss_range_stats[i].buy_total_rsi + profit_loss_range_stats[i].sell_total_rsi) / profit_loss_range_stats[i].total_trades;
            profit_rsi_sum += combined_rsi * profit_loss_range_stats[i].total_trades;
            profit_vol_sum += profit_loss_range_stats[i].avg_volatility * profit_loss_range_stats[i].total_trades;
            profit_trades += profit_loss_range_stats[i].total_trades;
            profit_count++;
        }
        else // 亏损区间
        {
            // 计算该区间的综合RSI（做多和做空的加权平均）
            double combined_rsi = (profit_loss_range_stats[i].buy_total_rsi + profit_loss_range_stats[i].sell_total_rsi) / profit_loss_range_stats[i].total_trades;
            loss_rsi_sum += combined_rsi * profit_loss_range_stats[i].total_trades;
            loss_vol_sum += profit_loss_range_stats[i].avg_volatility * profit_loss_range_stats[i].total_trades;
            loss_trades += profit_loss_range_stats[i].total_trades;
            loss_count++;
        }
    }

    if(profit_trades > 0)
    {
        Print("✅ 盈利交易的市场环境：RSI平均", DoubleToString(profit_rsi_sum/profit_trades, 1),
              "，波动平均", DoubleToString(profit_vol_sum/profit_trades, 0), "点 (", profit_trades, "笔交易)");
    }

    if(loss_trades > 0)
    {
        Print("❌ 亏损交易的市场环境：RSI平均", DoubleToString(loss_rsi_sum/loss_trades, 1),
              "，波动平均", DoubleToString(loss_vol_sum/loss_trades, 0), "点 (", loss_trades, "笔交易)");
    }

    if(profit_trades > 0 && loss_trades > 0)
    {
        double rsi_diff = (profit_rsi_sum/profit_trades) - (loss_rsi_sum/loss_trades);
        double vol_diff = (profit_vol_sum/profit_trades) - (loss_vol_sum/loss_trades);
        Print("🔍 关键发现：");
        Print("    盈利交易的RSI比亏损交易", (rsi_diff > 0 ? "高" : "低"), DoubleToString(MathAbs(rsi_diff), 1), "点");
        Print("    盈利交易的波动比亏损交易", (vol_diff > 0 ? "高" : "低"), DoubleToString(MathAbs(vol_diff), 0), "点");
    }

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 计算并立即扣除交易手续费                                           |
//+------------------------------------------------------------------+
double CalculateAndDeductCommission()
{
    // 修改说明：平仓后立即扣除固定手续费，模拟真实券商的手续费扣除时机
    double commission_amount = Trading_Commission_Fixed;

    // 更新手续费统计
    total_commission_paid += commission_amount;
    daily_commission_paid += commission_amount;
    commission_transactions++;

    Print("💳 平仓后立即扣除手续费：", DoubleToString(commission_amount, 2), " USD (固定手续费)");
    Print("📊 累计手续费支出：", DoubleToString(total_commission_paid, 2),
          " USD (", commission_transactions, "笔交易)");

    return commission_amount;
}

//+------------------------------------------------------------------+
//| 更新RSI区间统计                                                   |
//+------------------------------------------------------------------+
void UpdateRSIRangeStats(double rsi_value, double profit_loss, bool is_winning)
{
    // 确定RSI区间索引 (0-4=0, 5-9=1, 10-14=2, ..., 95-99=19)
    int range_index = (int)(rsi_value / 5.0);
    if(range_index < 0) range_index = 0;
    if(range_index > 19) range_index = 19;

    // 更新基本统计
    rsi_range_stats[range_index].total_trades++;

    if(is_winning)
    {
        rsi_range_stats[range_index].winning_trades++;
        rsi_range_stats[range_index].total_profit += profit_loss;
        if(profit_loss > rsi_range_stats[range_index].max_profit)
            rsi_range_stats[range_index].max_profit = profit_loss;
    }
    else
    {
        rsi_range_stats[range_index].losing_trades++;
        rsi_range_stats[range_index].total_loss += MathAbs(profit_loss);
        if(MathAbs(profit_loss) > rsi_range_stats[range_index].max_loss)
            rsi_range_stats[range_index].max_loss = MathAbs(profit_loss);
    }

    // 更新净盈利
    rsi_range_stats[range_index].net_profit = rsi_range_stats[range_index].total_profit - rsi_range_stats[range_index].total_loss;

    // 更新RSI值统计
    if(rsi_range_stats[range_index].total_trades == 1)
    {
        rsi_range_stats[range_index].avg_rsi = rsi_value;
        rsi_range_stats[range_index].min_rsi = rsi_value;
        rsi_range_stats[range_index].max_rsi = rsi_value;
    }
    else
    {
        // 更新平均RSI
        rsi_range_stats[range_index].avg_rsi = ((rsi_range_stats[range_index].avg_rsi * (rsi_range_stats[range_index].total_trades - 1)) + rsi_value) / rsi_range_stats[range_index].total_trades;

        // 更新最小最大RSI
        if(rsi_value < rsi_range_stats[range_index].min_rsi)
            rsi_range_stats[range_index].min_rsi = rsi_value;
        if(rsi_value > rsi_range_stats[range_index].max_rsi)
            rsi_range_stats[range_index].max_rsi = rsi_value;
    }

    // 计算胜率
    if(rsi_range_stats[range_index].total_trades > 0)
        rsi_range_stats[range_index].win_rate = (double)rsi_range_stats[range_index].winning_trades / rsi_range_stats[range_index].total_trades * 100.0;

    // 计算平均盈利和亏损
    if(rsi_range_stats[range_index].winning_trades > 0)
        rsi_range_stats[range_index].avg_profit = rsi_range_stats[range_index].total_profit / rsi_range_stats[range_index].winning_trades;
    if(rsi_range_stats[range_index].losing_trades > 0)
        rsi_range_stats[range_index].avg_loss = rsi_range_stats[range_index].total_loss / rsi_range_stats[range_index].losing_trades;

    // 计算盈利因子
    if(rsi_range_stats[range_index].total_loss > 0)
        rsi_range_stats[range_index].profit_factor = rsi_range_stats[range_index].total_profit / rsi_range_stats[range_index].total_loss;
    else if(rsi_range_stats[range_index].total_profit > 0)
        rsi_range_stats[range_index].profit_factor = 999.99; // 无亏损时设为高值

    int range_start = range_index * 5;
    int range_end = range_start + 4;
    Print("📊 更新RSI区间[", range_start, "-", range_end, "]统计：",
          is_winning ? "盈利" : "亏损", " ", DoubleToString(profit_loss, 2),
          " USD，RSI=", DoubleToString(rsi_value, 2));
}

//+------------------------------------------------------------------+
//| 更新波动值区间统计                                                 |
//+------------------------------------------------------------------+
void UpdateVolatilityRangeStats(double volatility_value, double profit_loss, bool is_winning)
{
    // 确定波动值区间索引 (115-164=0, 165-214=1, 215-264=2, ..., 565-614=9)
    int range_index = -1;

    if(volatility_value >= 115 && volatility_value <= 164) range_index = 0;
    else if(volatility_value >= 165 && volatility_value <= 214) range_index = 1;
    else if(volatility_value >= 215 && volatility_value <= 264) range_index = 2;
    else if(volatility_value >= 265 && volatility_value <= 314) range_index = 3;
    else if(volatility_value >= 315 && volatility_value <= 364) range_index = 4;
    else if(volatility_value >= 365 && volatility_value <= 414) range_index = 5;
    else if(volatility_value >= 415 && volatility_value <= 464) range_index = 6;
    else if(volatility_value >= 465 && volatility_value <= 514) range_index = 7;
    else if(volatility_value >= 515 && volatility_value <= 564) range_index = 8;
    else if(volatility_value >= 565 && volatility_value <= 614) range_index = 9;
    else
    {
        Print("⚠️ 波动值", DoubleToString(volatility_value, 1), "超出统计范围115-614");
        return; // 超出范围，不统计
    }

    // 更新基本统计
    volatility_range_stats[range_index].total_trades++;

    if(is_winning)
    {
        volatility_range_stats[range_index].winning_trades++;
        volatility_range_stats[range_index].total_profit += profit_loss;
        if(profit_loss > volatility_range_stats[range_index].max_profit)
            volatility_range_stats[range_index].max_profit = profit_loss;
    }
    else
    {
        volatility_range_stats[range_index].losing_trades++;
        volatility_range_stats[range_index].total_loss += MathAbs(profit_loss);
        if(MathAbs(profit_loss) > volatility_range_stats[range_index].max_loss)
            volatility_range_stats[range_index].max_loss = MathAbs(profit_loss);
    }

    // 更新净盈利
    volatility_range_stats[range_index].net_profit = volatility_range_stats[range_index].total_profit - volatility_range_stats[range_index].total_loss;

    // 更新波动值统计
    if(volatility_range_stats[range_index].total_trades == 1)
    {
        volatility_range_stats[range_index].avg_volatility = volatility_value;
        volatility_range_stats[range_index].min_volatility = volatility_value;
        volatility_range_stats[range_index].max_volatility = volatility_value;
    }
    else
    {
        // 更新平均波动值
        volatility_range_stats[range_index].avg_volatility = ((volatility_range_stats[range_index].avg_volatility * (volatility_range_stats[range_index].total_trades - 1)) + volatility_value) / volatility_range_stats[range_index].total_trades;

        // 更新最小最大波动值
        if(volatility_value < volatility_range_stats[range_index].min_volatility)
            volatility_range_stats[range_index].min_volatility = volatility_value;
        if(volatility_value > volatility_range_stats[range_index].max_volatility)
            volatility_range_stats[range_index].max_volatility = volatility_value;
    }

    // 计算胜率
    if(volatility_range_stats[range_index].total_trades > 0)
        volatility_range_stats[range_index].win_rate = (double)volatility_range_stats[range_index].winning_trades / volatility_range_stats[range_index].total_trades * 100.0;

    // 计算平均盈利和亏损
    if(volatility_range_stats[range_index].winning_trades > 0)
        volatility_range_stats[range_index].avg_profit = volatility_range_stats[range_index].total_profit / volatility_range_stats[range_index].winning_trades;
    if(volatility_range_stats[range_index].losing_trades > 0)
        volatility_range_stats[range_index].avg_loss = volatility_range_stats[range_index].total_loss / volatility_range_stats[range_index].losing_trades;

    // 计算盈利因子
    if(volatility_range_stats[range_index].total_loss > 0)
        volatility_range_stats[range_index].profit_factor = volatility_range_stats[range_index].total_profit / volatility_range_stats[range_index].total_loss;
    else if(volatility_range_stats[range_index].total_profit > 0)
        volatility_range_stats[range_index].profit_factor = 999.99; // 无亏损时设为高值

    int range_start = 115 + range_index * 50;
    int range_end = range_start + 49;
    Print("📊 更新波动值区间[", range_start, "-", range_end, "]统计：",
          is_winning ? "盈利" : "亏损", " ", DoubleToString(profit_loss, 2),
          " USD，波动值=", DoubleToString(volatility_value, 1));
}

//+------------------------------------------------------------------+
//| 打印波动值区间统计报告                                             |
//+------------------------------------------------------------------+
void PrintVolatilityRangeReport()
{
    Print("====================================================");
    Print("📊 波动值区间交易统计报告");
    Print("====================================================");
    Print("区间格式：[波动范围] | 交易次数 | 胜率% | 净盈利 | 盈利因子 | 平均波动");
    Print("----------------------------------------------------");

    double total_net_profit = 0;
    int total_all_trades = 0;
    int total_all_wins = 0;

    for(int i = 0; i < 10; i++)
    {
        if(volatility_range_stats[i].total_trades == 0) continue;

        int range_start = 115 + i * 50;
        int range_end = range_start + 49;

        total_net_profit += volatility_range_stats[i].net_profit;
        total_all_trades += volatility_range_stats[i].total_trades;
        total_all_wins += volatility_range_stats[i].winning_trades;

        string range_str = StringFormat("[%03d-%03d]", range_start, range_end);
        string trades_str = StringFormat("%3d", volatility_range_stats[i].total_trades);
        string win_rate_str = StringFormat("%5.1f%%", volatility_range_stats[i].win_rate);
        string net_profit_str = StringFormat("%8.2f", volatility_range_stats[i].net_profit);
        string profit_factor_str = StringFormat("%6.2f", volatility_range_stats[i].profit_factor);
        string avg_vol_str = StringFormat("%6.1f", volatility_range_stats[i].avg_volatility);

        Print(range_str, " | ", trades_str, " | ", win_rate_str, " | ", net_profit_str, " | ", profit_factor_str, " | ", avg_vol_str);

        // 详细信息（仅显示有交易的区间）
        Print("    详细: 盈利", volatility_range_stats[i].winning_trades, "次(+", DoubleToString(volatility_range_stats[i].total_profit, 2),
              ") 亏损", volatility_range_stats[i].losing_trades, "次(-", DoubleToString(volatility_range_stats[i].total_loss, 2), ")");
        Print("    极值: 最大盈利+", DoubleToString(volatility_range_stats[i].max_profit, 2),
              " 最大亏损-", DoubleToString(volatility_range_stats[i].max_loss, 2));
        Print("    平均: 盈利+", DoubleToString(volatility_range_stats[i].avg_profit, 2),
              " 亏损-", DoubleToString(volatility_range_stats[i].avg_loss, 2));
        Print("    波动范围: ", DoubleToString(volatility_range_stats[i].min_volatility, 1),
              " - ", DoubleToString(volatility_range_stats[i].max_volatility, 1), " 点");
        Print("    ----------------------------------------");
    }

    Print("====================================================");
    Print("📈 波动值区间统计汇总");
    Print("总交易次数：", total_all_trades);
    Print("总胜率：", total_all_trades > 0 ? DoubleToString((double)total_all_wins / total_all_trades * 100, 2) : "0.00", "%");
    Print("总净盈利：", DoubleToString(total_net_profit, 2), " USD (已扣除手续费)");
    Print("手续费影响：平仓后立即扣除", DoubleToString(Trading_Commission_Fixed, 0), "美元，累计", DoubleToString(total_commission_paid, 2), " USD");
    Print("====================================================");

    // 修改说明：添加最佳波动值区间分析
    AnalyzeBestVolatilityRanges();
}

//+------------------------------------------------------------------+
//| 分析最佳波动值区间                                                 |
//+------------------------------------------------------------------+
void AnalyzeBestVolatilityRanges()
{
    Print("🏆 波动值区间表现分析");
    Print("====================================================");

    // 找出最佳表现区间
    double best_net_profit = -999999;
    double best_win_rate = 0;
    double best_profit_factor = 0;
    int best_profit_range = -1;
    int best_winrate_range = -1;
    int best_pf_range = -1;
    int min_trades_threshold = 3; // 最少交易次数阈值

    for(int i = 0; i < 10; i++)
    {
        if(volatility_range_stats[i].total_trades < min_trades_threshold) continue;

        // 最佳净盈利区间
        if(volatility_range_stats[i].net_profit > best_net_profit)
        {
            best_net_profit = volatility_range_stats[i].net_profit;
            best_profit_range = i;
        }

        // 最佳胜率区间
        if(volatility_range_stats[i].win_rate > best_win_rate)
        {
            best_win_rate = volatility_range_stats[i].win_rate;
            best_winrate_range = i;
        }

        // 最佳盈利因子区间
        if(volatility_range_stats[i].profit_factor > best_profit_factor)
        {
            best_profit_factor = volatility_range_stats[i].profit_factor;
            best_pf_range = i;
        }
    }

    // 输出最佳区间
    if(best_profit_range >= 0)
    {
        int start = 115 + best_profit_range * 50;
        int end = start + 49;
        Print("💰 最佳净盈利波动区间：[", start, "-", end, "] 净盈利:", DoubleToString(best_net_profit, 2),
              " USD (", volatility_range_stats[best_profit_range].total_trades, "次交易)");
    }

    if(best_winrate_range >= 0)
    {
        int start = 115 + best_winrate_range * 50;
        int end = start + 49;
        Print("🎯 最佳胜率波动区间：[", start, "-", end, "] 胜率:", DoubleToString(best_win_rate, 1),
              "% (", volatility_range_stats[best_winrate_range].total_trades, "次交易)");
    }

    if(best_pf_range >= 0)
    {
        int start = 115 + best_pf_range * 50;
        int end = start + 49;
        Print("⚡ 最佳盈利因子波动区间：[", start, "-", end, "] 盈利因子:", DoubleToString(best_profit_factor, 2),
              " (", volatility_range_stats[best_pf_range].total_trades, "次交易)");
    }

    // 推荐交易区间
    Print("----------------------------------------------------");
    Print("📋 推荐波动值策略：");

    string recommended_ranges = "";
    int good_ranges = 0;

    for(int i = 0; i < 10; i++)
    {
        if(volatility_range_stats[i].total_trades < min_trades_threshold) continue;

        // 好区间的标准：胜率>50% 且 盈利因子>1.2 且 净盈利>0
        if(volatility_range_stats[i].win_rate > 50.0 &&
           volatility_range_stats[i].profit_factor > 1.2 &&
           volatility_range_stats[i].net_profit > 0)
        {
            int start = 115 + i * 50;
            int end = start + 49;
            if(good_ranges > 0) recommended_ranges += ", ";
            recommended_ranges += "[" + IntegerToString(start) + "-" + IntegerToString(end) + "]";
            good_ranges++;
        }
    }

    if(good_ranges > 0)
    {
        Print("✅ 建议在以下波动值区间交易：", recommended_ranges);
        Print("   (标准：胜率>50%，盈利因子>1.2，净盈利>0，最少", min_trades_threshold, "次交易)");
    }
    else
    {
        Print("⚠️ 暂无符合推荐标准的波动值区间，建议继续收集数据");
    }

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 打印RSI区间统计报告                                               |
//+------------------------------------------------------------------+
void PrintRSIRangeReport()
{
    Print("====================================================");
    Print("📊 RSI区间交易统计报告");
    Print("====================================================");
    Print("区间格式：[RSI范围] | 交易次数 | 胜率% | 净盈利 | 盈利因子 | 平均RSI");
    Print("----------------------------------------------------");

    double total_net_profit = 0;
    int total_all_trades = 0;
    int total_all_wins = 0;

    for(int i = 0; i < 20; i++)
    {
        if(rsi_range_stats[i].total_trades == 0) continue;

        int range_start = i * 5;
        int range_end = range_start + 4;

        total_net_profit += rsi_range_stats[i].net_profit;
        total_all_trades += rsi_range_stats[i].total_trades;
        total_all_wins += rsi_range_stats[i].winning_trades;

        string range_str = StringFormat("[%02d-%02d]", range_start, range_end);
        string trades_str = StringFormat("%3d", rsi_range_stats[i].total_trades);
        string win_rate_str = StringFormat("%5.1f%%", rsi_range_stats[i].win_rate);
        string net_profit_str = StringFormat("%8.2f", rsi_range_stats[i].net_profit);
        string profit_factor_str = StringFormat("%6.2f", rsi_range_stats[i].profit_factor);
        string avg_rsi_str = StringFormat("%6.2f", rsi_range_stats[i].avg_rsi);

        Print(range_str, " | ", trades_str, " | ", win_rate_str, " | ", net_profit_str, " | ", profit_factor_str, " | ", avg_rsi_str);

        // 详细信息（仅显示有交易的区间）
        Print("    详细: 盈利", rsi_range_stats[i].winning_trades, "次(+", DoubleToString(rsi_range_stats[i].total_profit, 2),
              ") 亏损", rsi_range_stats[i].losing_trades, "次(-", DoubleToString(rsi_range_stats[i].total_loss, 2), ")");
        Print("    极值: 最大盈利+", DoubleToString(rsi_range_stats[i].max_profit, 2),
              " 最大亏损-", DoubleToString(rsi_range_stats[i].max_loss, 2));
        Print("    平均: 盈利+", DoubleToString(rsi_range_stats[i].avg_profit, 2),
              " 亏损-", DoubleToString(rsi_range_stats[i].avg_loss, 2));
        Print("    RSI范围: ", DoubleToString(rsi_range_stats[i].min_rsi, 2),
              " - ", DoubleToString(rsi_range_stats[i].max_rsi, 2));
        Print("    ----------------------------------------");
    }

    Print("====================================================");
    Print("📈 RSI区间统计汇总");
    Print("总交易次数：", total_all_trades);
    Print("总胜率：", total_all_trades > 0 ? DoubleToString((double)total_all_wins / total_all_trades * 100, 2) : "0.00", "%");
    Print("总净盈利：", DoubleToString(total_net_profit, 2), " USD (已扣除手续费)");
    Print("手续费影响：平仓后立即扣除", DoubleToString(Trading_Commission_Fixed, 0), "美元，累计", DoubleToString(total_commission_paid, 2), " USD");
    Print("====================================================");

    // 修改说明：添加最佳RSI区间分析
    AnalyzeBestRSIRanges();
}

//+------------------------------------------------------------------+
//| 分析最佳RSI区间                                                   |
//+------------------------------------------------------------------+
void AnalyzeBestRSIRanges()
{
    Print("🏆 RSI区间表现分析");
    Print("====================================================");

    // 找出最佳表现区间
    double best_net_profit = -999999;
    double best_win_rate = 0;
    double best_profit_factor = 0;
    int best_profit_range = -1;
    int best_winrate_range = -1;
    int best_pf_range = -1;
    int min_trades_threshold = 3; // 最少交易次数阈值

    for(int i = 0; i < 20; i++)
    {
        if(rsi_range_stats[i].total_trades < min_trades_threshold) continue;

        // 最佳净盈利区间
        if(rsi_range_stats[i].net_profit > best_net_profit)
        {
            best_net_profit = rsi_range_stats[i].net_profit;
            best_profit_range = i;
        }

        // 最佳胜率区间
        if(rsi_range_stats[i].win_rate > best_win_rate)
        {
            best_win_rate = rsi_range_stats[i].win_rate;
            best_winrate_range = i;
        }

        // 最佳盈利因子区间
        if(rsi_range_stats[i].profit_factor > best_profit_factor)
        {
            best_profit_factor = rsi_range_stats[i].profit_factor;
            best_pf_range = i;
        }
    }

    // 输出最佳区间
    if(best_profit_range >= 0)
    {
        int start = best_profit_range * 5;
        int end = start + 4;
        Print("💰 最佳净盈利区间：[", start, "-", end, "] 净盈利:", DoubleToString(best_net_profit, 2),
              " USD (", rsi_range_stats[best_profit_range].total_trades, "次交易)");
    }

    if(best_winrate_range >= 0)
    {
        int start = best_winrate_range * 5;
        int end = start + 4;
        Print("🎯 最佳胜率区间：[", start, "-", end, "] 胜率:", DoubleToString(best_win_rate, 1),
              "% (", rsi_range_stats[best_winrate_range].total_trades, "次交易)");
    }

    if(best_pf_range >= 0)
    {
        int start = best_pf_range * 5;
        int end = start + 4;
        Print("⚡ 最佳盈利因子区间：[", start, "-", end, "] 盈利因子:", DoubleToString(best_profit_factor, 2),
              " (", rsi_range_stats[best_pf_range].total_trades, "次交易)");
    }

    // 推荐交易区间
    Print("----------------------------------------------------");
    Print("📋 推荐交易策略：");

    string recommended_ranges = "";
    int good_ranges = 0;

    for(int i = 0; i < 20; i++)
    {
        if(rsi_range_stats[i].total_trades < min_trades_threshold) continue;

        // 好区间的标准：胜率>50% 且 盈利因子>1.2 且 净盈利>0
        if(rsi_range_stats[i].win_rate > 50.0 &&
           rsi_range_stats[i].profit_factor > 1.2 &&
           rsi_range_stats[i].net_profit > 0)
        {
            int start = i * 5;
            int end = start + 4;
            if(good_ranges > 0) recommended_ranges += ", ";
            recommended_ranges += "[" + IntegerToString(start) + "-" + IntegerToString(end) + "]";
            good_ranges++;
        }
    }

    if(good_ranges > 0)
    {
        Print("✅ 建议在以下RSI区间交易：", recommended_ranges);
        Print("   (标准：胜率>50%，盈利因子>1.2，净盈利>0，最少", min_trades_threshold, "次交易)");
    }
    else
    {
        Print("⚠️ 暂无符合推荐标准的RSI区间，建议继续收集数据");
    }

    Print("====================================================");
}

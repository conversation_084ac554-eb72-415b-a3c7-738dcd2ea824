//+------------------------------------------------------------------+
//|                                    US30_TrendFollowing_EA.mq5 |
//|                          【US30道指专用】波动突破顺势策略EA - 专业版 |
//|                     专为US30优化 | 顺势突破策略 | 高频交易 | 专业级 |
//+------------------------------------------------------------------+
#property copyright "US30 Dow Jones Trend Following Strategy - Professional"
#property version   "1.00"
#property strict

// 输入参数
input double   Risk_Percent_Per_Trade    = 7;     // 每笔风险控制百分比（迷你US30优化：7%，适应迷你合约）
input double   Spike_Range_Multiplier    = 1.05;     // 波动异常触发倍数（US30优化：1.05，适应道指高频波动）
input int      Spike_Min_Points          = 1250;     // 波动最小阈值（点）（US30优化：1250点，适应道指大波动特性）
input int      Spike_Max_Points          = 16500;     // 波动最大阈值（点）（US30优化：16500点，适应道指极端波动）
input bool     Volume_Spike_Enabled      = true;    // 是否启用成交量过滤（优化：启用，提升信号质量）
input int      Volume_MA_Period          = 10;      // 成交量均值周期（标准设置：10周期）
input double   Volume_Threshold_Mult     = 1.05;    // 成交量倍数阈值（US30优化：1.05，适应道指成交量特性）
input int      StopLoss_Points           = 3000;     // 止损点位（US30优化：3000点，适应道指大波动）
// input int      Reverse_StopLoss_Points   = 65;      // 反向交易止损点位（已移除，US30使用顺势策略）
input int      TakeProfit_Points         = 10000;     // 止盈点位（US30优化：10000点，适应道指大幅趋势）
input int      Max_Trades_Per_Day        = 188;     // 每天最多交易次数（US30优化：188次，适应高频交易）
input double   Max_Drawdown_Per_Day      = 20.0;    // 最大日亏限制百分比（US30优化：20%，适应大波动风险）
input double   Max_Equity_Drawdown       = 30.0;    // 最大净值回撤百分比（US30优化：30%峰值回撤保护）
input bool     Manual_Reset_Drawdown     = false;   // 人工重置回撤保护（设为true可手动解除暂停）
input int      Start_Trading_Hour        = 0;       // 开始交易时间（US30优化：24小时交易，适应全球市场）
input int      End_Trading_Hour          = 23;      // 结束交易时间（US30优化：24小时交易，捕捉所有机会）
input int      Start_Trading_Minute      = 0;       // 开始交易分钟（US30优化：全时段交易）
input int      End_Trading_Minute        = 59;      // 结束交易分钟（US30优化：全时段交易）
input int      Max_Slippage              = 30;      // 最大滑点限制（US30优化：30点，适应道指滑点）
input bool     Use_Reversal_Strategy     = false;     // 是否使用反转策略（修改说明：US30使用顺势策略，跟随趋势方向）
input bool     RSI_Filter_Enabled        = false;     // 是否启用RSI过滤（US30优化：可选启用）
input int      RSI_Period                = 14;      // RSI周期（US30优化：标准14周期）
input double   RSI_Oversold              = 40;      // RSI超卖阈值（US30优化：40，适应道指波动特性）
input double   RSI_Overbought            = 55;      // RSI超买下限阈值（US30优化：55，超买区间下限）
input double   RSI_Overbought_Upper      = 85;      // RSI超买上限阈值（US30优化：85，超买区间上限）
input bool     EMA_Trend_Filter          = true;     // 是否启用EMA趋势过滤（US30优化：启用，确认趋势方向）
input int      EMA_Fast_Period           = 3;      // 快速EMA周期（US30优化：3周期，适应道指快速变化）
input int      EMA_Slow_Period           = 15;      // 慢速EMA周期（US30优化：15周期，平衡灵敏度）
input bool     Trailing_Stop_Enabled     = true;     // 是否启用移动止损（US30优化：启用）
// 修改说明：已移除保本保护参数
// input bool     Breakeven_Protection       = true;    // 是否启用保本保护（已移除）
// input int      Breakeven_Trigger_Points   = 50;      // 保本触发点数（已移除）
// input int      Breakeven_Lock_Points      = 50;      // 保本锁定点数（已移除）
// 做多移动止损参数
input int      Trailing_Stop_Distance_Buy = 200;     // 做多移动止损距离（点）（US30优化：200点，适应道指大波动）
input int      Trailing_Stop_Step_Buy     = 30;      // 做多移动止损步长（点）（US30优化：30点步长）
// 做空移动止损参数
input int      Trailing_Stop_Distance_Sell = 200;    // 做空移动止损距离（点）（US30优化：200点，与做多保持一致）
input int      Trailing_Stop_Step_Sell     = 30;     // 做空移动止损步长（点）（US30优化：30点步长）
// US30顺势策略移动止损优化参数
// input int      Second_Stop_Profit_Points  = 35;     // 第二次止损利润保护点数（修改说明：已移除利润保护功能）
input int      Account_Report_Minutes   = 30;      // 账户报告间隔（分钟）

// 修改说明：新增电报通知功能参数（如果网络问题可设为false）
input bool     Telegram_Enabled          = true;   // 是否启用电报通知
input string   Telegram_Bot_Token        = "**********************************************";      // 电报机器人Token
input string   Telegram_Chat_ID          = "**********";      // 电报聊天ID
input bool     Telegram_Trade_Alerts     = true;    // 交易完成通知
input bool     Telegram_Daily_Report     = true;    // 每日账户报告
input int      Daily_Report_Hour         = 23;      // 每日报告时间（小时）

// 全局指标句柄
int rsi_handle = INVALID_HANDLE;
int ema_fast_handle = INVALID_HANDLE;
int ema_slow_handle = INVALID_HANDLE;
bool rsi_filter_active = false;  // RSI过滤是否实际激活
bool ema_filter_active = false;  // EMA过滤是否实际激活

// 全局变量
datetime last_bar_time = 0;        // 上一根K线时间
int daily_trade_count = 0;         // 当日成功交易次数
int daily_trade_attempts = 0;      // 当日交易尝试次数

// 修改说明：电报通知相关全局变量
datetime last_daily_report_time = 0;  // 上次每日报告时间
bool telegram_initialized = false;    // 电报是否初始化成功
double daily_start_balance = 0;    // 当日开始余额
double initial_balance = 0;        // 初始余额（回测开始时的余额）
datetime current_date = 0;         // 当前日期
datetime last_heartbeat = 0;      // 上次心跳时间

// 修改说明：已移除保本保护相关全局变量
// bool breakeven_activated = false;     // 保本保护是否已激活（已移除）
// double position_open_price = 0;       // 持仓开仓价格（已移除）
// ulong position_ticket = 0;            // 当前持仓票号（已移除）

// 修改说明：新增30%峰值回撤保护相关变量
double equity_peak = 0;                    // 账户净值峰值
bool trading_paused_by_drawdown = false;   // 是否因回撤暂停交易
datetime drawdown_pause_start = 0;         // 回撤暂停开始时间
datetime last_account_report = 0;  // 上次账户报告时间
bool daily_limit_printed = false;  // 日限制提示标志
string ea_name = "US30_TrendFollowing_EA_v1.00_Professional_Optimized";

// 修改说明：已移除亏损后反向交易相关变量
// bool last_trade_was_loss = false;        // 上一笔交易是否亏损（已移除）
// string last_trade_direction = "";        // 上一笔交易方向（已移除）
// datetime last_loss_time = 0;             // 上一次亏损时间（已移除）
// bool reverse_trade_pending = false;      // 是否有待执行的反向交易（已移除）
// int consecutive_reverse_count = 0;       // 连续反向交易次数（已移除）
// int max_reverse_trades = 1;              // 最大连续反向交易次数限制（已移除）
// bool is_reverse_trade = false;           // 标记当前交易是否为反转交易（已移除）

// 修改说明：移动止损优化相关变量（已移除利润保护功能）
// bool second_stop_applied = false;        // 是否已应用利润保护止损（已移除）
ulong current_position_ticket = 0;       // 当前持仓票号，用于跟踪止损状态

// 修改说明：新增日亏损限制统计变量
int daily_loss_limit_hit_count = 0;     // 日亏损超过限制的次数（累计）
bool daily_loss_limit_hit_today = false; // 今日是否已触发日亏损限制

// 修改说明：增强统计结构，添加做多做空分别统计
struct TradeStats
{
    int total_trades;              // 总交易次数
    int winning_trades;            // 盈利交易次数
    int losing_trades;             // 亏损交易次数
    double total_profit;           // 总盈利
    double total_loss;             // 总亏损
    double max_profit;             // 最大单笔盈利
    double max_loss;               // 最大单笔亏损
    double max_drawdown;           // 最大回撤
    double max_balance;            // 最大余额
    int consecutive_wins;          // 连续盈利次数
    int consecutive_losses;        // 连续亏损次数
    int max_consecutive_wins;      // 最大连续盈利
    int max_consecutive_losses;    // 最大连续亏损
    int volatility_signals;        // 波动信号总数
    int volume_signals;            // 成交量信号总数
    int filtered_signals;          // 被过滤的信号数
    datetime first_trade_time;     // 首次交易时间
    datetime last_trade_time;      // 最后交易时间

    // 做多统计
    int buy_total;                 // 做多总次数
    int buy_wins;                  // 做多盈利次数
    int buy_losses;                // 做多亏损次数
    double buy_profit;             // 做多总盈利
    double buy_loss;               // 做多总亏损
    double buy_max_profit;         // 做多最大盈利
    double buy_max_loss;           // 做多最大亏损

    // 做空统计
    int sell_total;                // 做空总次数
    int sell_wins;                 // 做空盈利次数
    int sell_losses;               // 做空亏损次数
    double sell_profit;            // 做空总盈利
    double sell_loss;              // 做空总亏损
    double sell_max_profit;        // 做空最大盈利
    double sell_max_loss;          // 做空最大亏损

    // 过滤统计
    int rsi_filtered;              // RSI过滤次数
    int ema_filtered;              // EMA过滤次数
    int time_filtered;             // 时间过滤次数
    int daily_limit_filtered;      // 日限制过滤次数
};

TradeStats stats;                  // 统计数据结构
datetime last_report_time = 0;     // 上次报告时间

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化EA参数验证
    if(Risk_Percent_Per_Trade <= 0 || Risk_Percent_Per_Trade > 10)
    {
        Print("错误：风险百分比必须在0-10%之间");
        return INIT_PARAMETERS_INCORRECT;
    }

    if(StopLoss_Points <= 0 || TakeProfit_Points <= 0)
    {
        Print("错误：止损止盈点位必须大于0");
        return INIT_PARAMETERS_INCORRECT;
    }

    // 修改说明：初始化统计数据结构
    ZeroMemory(stats);
    stats.max_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // 初始化日期和余额
    current_date = TimeCurrent();
    daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    initial_balance = AccountInfoDouble(ACCOUNT_BALANCE);  // 修改说明：记录初始余额用于正确计算总收益率
    daily_trade_count = 0;
    daily_trade_attempts = 0;
    last_report_time = TimeCurrent();

    // 修改说明：初始化30%峰值回撤保护变量
    equity_peak = AccountInfoDouble(ACCOUNT_EQUITY);
    trading_paused_by_drawdown = false;
    drawdown_pause_start = 0;

    // 修改说明：已移除反向交易相关变量初始化
    // last_trade_was_loss = false;     // 已移除
    // last_trade_direction = "";       // 已移除
    // last_loss_time = 0;              // 已移除
    // reverse_trade_pending = false;   // 已移除
    // consecutive_reverse_count = 0;   // 已移除
    // is_reverse_trade = false;        // 已移除

    // 修改说明：初始化移动止损优化相关变量（已移除利润保护功能）
    // second_stop_applied = false;  // 已移除
    current_position_ticket = 0;

    // 修改说明：初始化日亏损限制统计变量
    daily_loss_limit_hit_count = 0;
    daily_loss_limit_hit_today = false;

    // 修改说明：初始化RSI指标句柄
    if(RSI_Filter_Enabled)
    {
        rsi_handle = iRSI(Symbol(), PERIOD_M1, RSI_Period, PRICE_CLOSE);
        if(rsi_handle == INVALID_HANDLE)
        {
            Print("❌ RSI指标初始化失败，禁用RSI过滤");
            rsi_filter_active = false;
        }
        else
        {
            Print("✅ RSI指标初始化成功");
            rsi_filter_active = true;
        }
    }
    else
    {
        rsi_filter_active = false;
    }

    // 修改说明：初始化EMA指标句柄
    if(EMA_Trend_Filter)
    {
        ema_fast_handle = iMA(Symbol(), PERIOD_M1, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
        ema_slow_handle = iMA(Symbol(), PERIOD_M1, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);

        if(ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE)
        {
            Print("❌ EMA指标初始化失败，禁用EMA过滤");
            ema_filter_active = false;
        }
        else
        {
            Print("✅ EMA指标初始化成功");
            ema_filter_active = true;
        }
    }
    else
    {
        ema_filter_active = false;
    }

    Print("=== ", ea_name, " 初始化成功 ===");
    Print("🏆 【US30道指专用】波动突破顺势策略EA - 专业版");
    Print("📈 专为US30优化 | 顺势突破策略 | 高频交易 | 移动止损优化");
    Print("🔄 策略核心：纯顺势交易逻辑 | 跟随趋势方向 | 专注突破信号");
    Print("🎯 移动止损：直接按350点距离移动止损，适应US30大波动");
    Print("⚡ 移动止损优先运算：每个Tick最高优先级处理移动止损，确保实时响应价格变化");
    Print("🔥 EA正在运行中，等待US30市场信号...");
    Print("📊 当前时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("💰 当前余额：", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD");

    // 修改说明：US30专用验证
    string current_symbol = Symbol();
    if(StringFind(current_symbol, "US30") >= 0 || StringFind(current_symbol, "DOW") >= 0 || StringFind(current_symbol, "DJ30") >= 0)
    {
        Print("✅ US30道指品种验证通过：", current_symbol);
    }
    else
    {
        Print("⚠️ 警告：此EA专为US30道指优化，当前品种：", current_symbol);
        Print("⚠️ 建议使用US30等道指品种以获得最佳效果");
    }
    Print("交易品种: ", Symbol());
    Print("当前余额: ", daily_start_balance);
    Print("风险控制: ", Risk_Percent_Per_Trade, "%");
    Print("统计报告间隔: 6小时");

    // 修改说明：参数验证，确保当前设置正确
    Print("=== 参数验证（当前优化设置）===");
    Print("波动倍数: ", Spike_Range_Multiplier, " (当前值：1.3)");
    Print("最小波动: ", Spike_Min_Points, " 点 (当前值：115)");
    Print("最大波动: ", Spike_Max_Points, " 点 (修改：600点上限，更严格避免极端波动)");
    Print("成交量倍数: ", Volume_Threshold_Mult, " (当前值：1.05)");
    Print("EMA快线: ", EMA_Fast_Period, " (当前值：12)");
    Print("EMA慢线: ", EMA_Slow_Period, " (当前值：30)");
    Print("止损: ", StopLoss_Points, " 点 (当前值：125)");
    // Print("反向交易止损: ", Reverse_StopLoss_Points, " 点 (当前值：65)");  // 已移除反向交易功能
    Print("止盈: ", TakeProfit_Points, " 点 (当前值：700)");
    Print("风险百分比: ", Risk_Percent_Per_Trade, "% (当前值：1.5%)");
    Print("日交易限制: ", Max_Trades_Per_Day, " 次 (当前值：188)");
    Print("日亏损限制: ", Max_Drawdown_Per_Day, "% (当前值：20.0%)");
    Print("净值回撤保护: ", Max_Equity_Drawdown, "% (新增：30%峰值回撤保护)");
    Print("移动止损: 做多", Trailing_Stop_Distance_Buy, "点/", Trailing_Stop_Step_Buy, "步，做空", Trailing_Stop_Distance_Sell, "点/", Trailing_Stop_Step_Sell, "步");
    Print("保本保护: 已移除 (修改说明：简化移动止损逻辑)");
    Print("RSI过滤: ", rsi_filter_active ? "启用" : "禁用", " (当前：启用，超卖<40，超买55-85，30-40区间需波动>180点)");
    Print("📱 电报通知: ", Telegram_Enabled ? "启用" : "禁用", " (交易通知:", Telegram_Trade_Alerts ? "开" : "关", "，每日报告:", Telegram_Daily_Report ? "开" : "关", ")");
    Print("EMA过滤: ", ema_filter_active ? "启用" : "禁用", " (当前：启用)");
    Print("交易策略: ", Use_Reversal_Strategy ? "反转策略" : "顺势策略", " (当前：顺势)");
    Print("策略说明: 纯顺势交易策略（阳线做多，阴线做空），跟随US30趋势方向");
    Print("止损设置: 统一使用", StopLoss_Points, "点止损");
    Print("移动止损: 统一的直接移动止损逻辑（350点距离直接跟随，适应US30大波动）");
    Print("交易时间: 24小时连续交易 (仅周一开盘后30分钟和周五闭盘前30分钟禁止)");
    Print("时间规则详情:");
    Print("  - 周一：01:30-23:59（开盘后30分钟禁止交易）");
    Print("  - 周二-周四：00:00-23:59（24小时连续交易，无限制）");
    Print("  - 周五：00:00-23:29（24小时交易，闭盘前30分钟禁止）");
    Print("  - 周末：不交易");

    // 修改说明：更新参数检查为US30顺势策略优化值，与实际input参数保持一致
    if(Spike_Range_Multiplier != 1.05 || Spike_Min_Points != 1250 || Spike_Max_Points != 16500 || StopLoss_Points != 3000 || TakeProfit_Points != 10000 ||
       RSI_Oversold != 40 || RSI_Overbought != 55 || RSI_Overbought_Upper != 85 || Max_Drawdown_Per_Day != 20.0 || Max_Trades_Per_Day != 188 || Use_Reversal_Strategy != false)
    {
        Print("❌❌❌ 警告：参数未更新！请检查参数设置！❌❌❌");
        Print("期望值：波动倍数1.05，最小波动1250，最大波动16500，止损3000，止盈10000，RSI阈值40/55-85，日交易限制188次，日亏损限制20%，顺势策略");
        Print("当前值：波动倍数", Spike_Range_Multiplier, "，最小波动", Spike_Min_Points, "，最大波动", Spike_Max_Points, "，止损", StopLoss_Points, "，止盈", TakeProfit_Points);
        Print("RSI阈值：", RSI_Oversold, "/", RSI_Overbought, "，日交易限制：", Max_Trades_Per_Day, "次，日亏损限制：", Max_Drawdown_Per_Day, "%，策略：", Use_Reversal_Strategy ? "反转" : "顺势");
    }
    else
    {
        Print("✅✅✅ 参数验证成功：使用US30优化参数（波动倍数1.05，波动范围1250-16500点，止损3000点，止盈10000点，RSI阈值40/55-85，日交易限制188次，日亏损限制20%，顺势策略，移动止损200点，已移除保本保护）！✅✅✅");
    }
    Print("==========================================");

    // 修改说明：初始化电报通知功能（如果失败不影响EA运行）
    if(Telegram_Enabled)
    {
        Print("📱 正在初始化电报通知...");
        InitializeTelegramNotification();
        if(!telegram_initialized)
        {
            Print("⚠️ 电报通知初始化失败，但EA将继续正常运行");
            Print("💡 如需禁用电报通知，请将Telegram_Enabled设为false");
            Print("💡 或者检查WebRequest权限：工具->选项->专家顾问->允许WebRequest");
        }
        else
        {
            Print("✅ 电报通知初始化成功，交易通知已启用");
        }
    }
    else
    {
        Print("📱 电报通知已禁用");
        telegram_initialized = false;
    }

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("=== ", ea_name, " EA 停止运行 ===");
    Print("停止原因代码: ", reason);

    // 修改说明：释放指标句柄
    if(rsi_handle != INVALID_HANDLE)
    {
        IndicatorRelease(rsi_handle);
        Print("✅ RSI指标句柄已释放");
    }
    if(ema_fast_handle != INVALID_HANDLE)
    {
        IndicatorRelease(ema_fast_handle);
        Print("✅ EMA快线句柄已释放");
    }
    if(ema_slow_handle != INVALID_HANDLE)
    {
        IndicatorRelease(ema_slow_handle);
        Print("✅ EMA慢线句柄已释放");
    }

    // 修改说明：实盘模式不使用历史统计分析，直接输出当前统计
    PrintFinalReport();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 修改说明：移动止损优先运算 - 最高优先级处理移动止损
    if(Trailing_Stop_Enabled && PositionExists())
    {
        UpdateTrailingStop();
    }

    // 修改说明：每次Tick都更新统计数据
    UpdateTradeStatistics();

    // 修改说明：添加心跳功能，每5分钟输出一次状态确认EA在运行
    datetime current_time = TimeCurrent();
    if(current_time - last_heartbeat >= 300) // 5分钟 = 300秒
    {
        last_heartbeat = current_time;
        Print("💓 EA心跳检测 - 服务器时间:", TimeToString(current_time, TIME_DATE|TIME_MINUTES),
              " | 本地时间:", TimeToString(TimeLocal(), TIME_DATE|TIME_MINUTES),
              " | 余额: ", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD",
              " | 今日交易: ", daily_trade_count, " 次");
    }

    // 修改说明：定时输出详细账户报告
    if(current_time - last_account_report >= Account_Report_Minutes * 60)
    {
        last_account_report = current_time;
        PrintAccountReport();
    }

    // 修改说明：检查每日电报报告
    CheckDailyTelegramReport();

    // 修改说明：已移除反向交易检查
    // if(reverse_trade_pending && !PositionExists())  // 已移除反向交易功能
    // {
    //     ExecuteReverseTrade();
    //     return;
    // }

    // 检查是否为新的M1 K线
    if(!IsNewM1Bar()) return;

    // 修改说明：添加6小时统计报告
    CheckAndPrintPeriodicReport();

    // 修改说明：检查30%峰值回撤保护，优先级最高
    if(IsEquityDrawdownLimitReached())
    {
        stats.filtered_signals++;
        Print("🛑 跳过信号：账户净值回撤超过30%，交易已暂停");
        return;
    }

    // 检查是否已有持仓
    if(PositionExists())
    {
        Print("⏸️ 跳过信号：已有持仓");
        return;
    }

    // 检查交易时间
    if(!IsTimeToTrade())
    {
        stats.filtered_signals++;
        Print("⏰ 跳过信号：非交易时间");
        return;
    }

    // 检查日交易限制
    if(IsDailyTradeLimitReached())
    {
        stats.filtered_signals++;
        Print("🚫 跳过信号：已达日交易限制 (", daily_trade_count, "/", Max_Trades_Per_Day, ")");
        return;
    }

    // 检查日亏损限制
    if(IsDailyLossLimitReached())
    {
        stats.filtered_signals++;
        Print("💸 跳过信号：已达日亏损限制");
        return;
    }

    // 检查波动异常和成交量异常
    bool volatility_spike = IsVolatilitySpike();
    bool volume_spike = (!Volume_Spike_Enabled || IsVolumeSpike());

    if(volatility_spike)
    {
        stats.volatility_signals++;
        Print("🔥 波动异常检测成功");
    }
    if(volume_spike && Volume_Spike_Enabled) stats.volume_signals++;

    // 修改说明：添加信号组合状态调试
    if(!volatility_spike && volume_spike)
    {
        Print("⚠️ 仅成交量异常，缺少波动异常");
    }
    else if(volatility_spike && !volume_spike)
    {
        Print("⚠️ 仅波动异常，缺少成交量异常");
    }
    else if(!volatility_spike && !volume_spike)
    {
        static datetime last_no_signal_time = 0;
        if(TimeCurrent() - last_no_signal_time >= 300) // 每5分钟输出一次
        {
            last_no_signal_time = TimeCurrent();
            Print("📊 市场平静：无波动异常，无成交量异常");
        }
    }

    if(volatility_spike && volume_spike)
    {
        Print("🎯 双重异常信号确认：波动异常 + 成交量异常");
        string direction = GetBarDirection();
        Print("📊 K线方向：", direction);

        // 修改说明：添加RSI和EMA双重过滤，提高信号质量
        bool rsi_passed = IsRSIFilterPassed(direction);
        bool ema_passed = IsEMATrendFilterPassed(direction);

        Print("🔍 过滤器检查：RSI ", (rsi_passed ? "✅通过" : "❌未通过"),
              "，EMA ", (ema_passed ? "✅通过" : "❌未通过"));

        if(rsi_passed && ema_passed)
        {
            Print("✅ 所有过滤器通过，准备开仓");
            double lots = CalculateLotSize();

            if(lots > 0)
            {
                daily_trade_attempts++;
                // is_reverse_trade = false; // 修改说明：已移除反向交易标记
                OpenTrade(direction, lots);
            }
            else
            {
                stats.filtered_signals++;
                Print("❌ 手数计算失败，跳过交易 (余额不足或风险过高)");
            }
        }
        else
        {
            stats.filtered_signals++;
            bool rsi_passed = IsRSIFilterPassed(direction);
            bool ema_passed = IsEMATrendFilterPassed(direction);

            if(!rsi_passed)
            {
                stats.rsi_filtered++;
                Print("🔍 RSI过滤：", direction, " 信号被过滤");
            }
            if(!ema_passed)
            {
                stats.ema_filtered++;
                Print("🔍 EMA过滤：", direction, " 信号被过滤");
            }
        }
    }
    else
    {
        if(volatility_spike || (Volume_Spike_Enabled && volume_spike))
        {
            stats.filtered_signals++;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查是否为新的M1 K线                                              |
//+------------------------------------------------------------------+
bool IsNewM1Bar()
{
    datetime current_bar_time = iTime(Symbol(), PERIOD_M1, 0);
    
    if(current_bar_time != last_bar_time)
    {
        last_bar_time = current_bar_time;
        
        // 检查是否为新的一天，重置日计数器
        MqlDateTime dt;
        TimeToStruct(current_bar_time, dt);
        datetime today = StructToTime(dt) - dt.hour*3600 - dt.min*60 - dt.sec;
        
        if(today != current_date)
        {
            current_date = today;
            daily_trade_count = 0;
            daily_trade_attempts = 0;
            daily_start_balance = AccountInfoDouble(ACCOUNT_BALANCE);
            daily_limit_printed = false;  // 重置日限制提示标志
            // consecutive_reverse_count = 0;  // 修改说明：已移除反向交易计数器
            daily_loss_limit_hit_today = false;  // 修改说明：重置今日日亏损限制触发标志

            Print("新的交易日开始，重置计数器 - 日期:", TimeToString(today, TIME_DATE));
        }
        
        return true;
    }
    
    return false;
}

//+------------------------------------------------------------------+
//| 检查保本保护条件（已移除功能）                                      |
//+------------------------------------------------------------------+
// void CheckBreakevenProtection(ENUM_POSITION_TYPE position_type, double current_bid, double current_ask, double open_price, double point)  // 已移除保本保护功能
// {
//     if(!Breakeven_Protection || breakeven_activated) return;
//     double profit_points = 0;
//     if(position_type == POSITION_TYPE_BUY)
//     {
//         profit_points = (current_bid - open_price) / point;
//     }
//     else if(position_type == POSITION_TYPE_SELL)
//     {
//         profit_points = (open_price - current_ask) / point;
//     }
//     if(profit_points >= Breakeven_Trigger_Points)
//     {
//         breakeven_activated = true;
//         Print("🛡️ 保本保护已激活！盈利 ", DoubleToString(profit_points, 0), " 点，达到触发条件 ",
//               Breakeven_Trigger_Points, " 点，保本锁定 +", Breakeven_Lock_Points, " 点");
//     }
// }

//+------------------------------------------------------------------+
//| 检查是否存在持仓                                                  |
//+------------------------------------------------------------------+
bool PositionExists()
{
    return PositionSelect(Symbol());
}

//+------------------------------------------------------------------+
//| 检查交易时间                                                      |
//+------------------------------------------------------------------+
bool IsTimeToTrade()
{
    MqlDateTime dt;
    TimeCurrent(dt);
    int current_hour = dt.hour;
    int current_minute = dt.min;
    int day_of_week = dt.day_of_week;

    // 修改说明：实现用户要求的精确交易时间控制
    // 要求：周内交易时间01:02-23:59，开盘后半小时内禁止交易，闭盘前半小时内禁止交易

    // 周末不交易（周六=6，周日=0）
    if(day_of_week == 0 || day_of_week == 6)
    {
        Print("⏰ 非交易时间：周末不交易 (周", day_of_week, ")");
        return false;
    }

    // 周一特殊处理：开盘后半小时内禁止交易（00:00-01:29禁止）
    if(day_of_week == 1)
    {
        if(current_hour == 0 || (current_hour == 1 && current_minute < 30))
        {
            Print("⏰ 非交易时间：周一开盘后半小时内禁止交易 (",
                  StringFormat("%02d:%02d", current_hour, current_minute), ")");
            return false;
        }
    }

    // 周五特殊处理：闭盘前半小时内禁止交易（23:30-23:59禁止）
    if(day_of_week == 5)
    {
        if(current_hour == 23 && current_minute >= 30)
        {
            Print("⏰ 非交易时间：周五闭盘前半小时内禁止交易 (",
                  StringFormat("%02d:%02d", current_hour, current_minute), ")");
            return false;
        }
    }

    // 修改说明：周二、周三、周四为24小时连续交易，无任何时间限制

    // 修改说明：外汇市场周内24小时连续交易，无需基础时间范围限制
    // 只需要检查开盘后30分钟和闭盘前30分钟的特殊限制即可

    // 通过所有时间检查
    return true;
}

//+------------------------------------------------------------------+
//| 检查日交易次数限制                                                |
//+------------------------------------------------------------------+
bool IsDailyTradeLimitReached()
{
    // 修改说明：只计算成功的交易次数，不计算失败的尝试
    if(daily_trade_count >= Max_Trades_Per_Day)
    {
        // 只在第一次达到限制时打印，避免重复日志
        if(!daily_limit_printed)
        {
            stats.daily_limit_filtered++;
            Print("已达到每日最大交易次数限制: ", Max_Trades_Per_Day, " (成功交易:", daily_trade_count, ", 尝试次数:", daily_trade_attempts, ")");
            daily_limit_printed = true;
        }
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查日亏损限制                                                    |
//+------------------------------------------------------------------+
bool IsDailyLossLimitReached()
{
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double daily_loss_percent = (daily_start_balance - current_balance) / daily_start_balance * 100;

    if(daily_loss_percent >= Max_Drawdown_Per_Day)
    {
        // 修改说明：首次触发日亏损限制时增加计数
        if(!daily_loss_limit_hit_today)
        {
            daily_loss_limit_hit_count++;
            daily_loss_limit_hit_today = true;
            Print("🚨 首次触发日亏损限制: ", Max_Drawdown_Per_Day, "%, 当前亏损: ", daily_loss_percent, "% (累计触发次数:", daily_loss_limit_hit_count, ")");
        }
        else
        {
            Print("已达到每日最大亏损限制: ", Max_Drawdown_Per_Day, "%, 当前亏损: ", daily_loss_percent, "%");
        }
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| 检查30%净值回撤限制                                              |
//+------------------------------------------------------------------+
bool IsEquityDrawdownLimitReached()
{
    // 修改说明：实现30%峰值回撤保护机制
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);

    // 检查人工重置标志
    if(Manual_Reset_Drawdown && trading_paused_by_drawdown)
    {
        trading_paused_by_drawdown = false;
        drawdown_pause_start = 0;
        equity_peak = current_equity; // 重置峰值为当前净值
        Print("🔧 人工重置回撤保护，交易恢复");
        Print("📈 重置净值峰值为：", DoubleToString(equity_peak, 2), " USD");
        Print("⚠️  请将Manual_Reset_Drawdown参数改回false");
    }

    // 更新净值峰值
    if(current_equity > equity_peak)
    {
        equity_peak = current_equity;
        // 如果净值创新高，重置暂停状态（允许人工解除后重新开始交易）
        if(trading_paused_by_drawdown)
        {
            trading_paused_by_drawdown = false;
            drawdown_pause_start = 0;
            Print("✅ 净值创新高，自动解除回撤保护暂停状态");
            Print("📈 新的净值峰值：", DoubleToString(equity_peak, 2), " USD");
        }
    }

    // 计算当前回撤百分比
    double current_drawdown_percent = 0;
    if(equity_peak > 0)
    {
        current_drawdown_percent = (equity_peak - current_equity) / equity_peak * 100;
    }

    // 检查是否触发30%回撤保护
    if(current_drawdown_percent >= Max_Equity_Drawdown && !trading_paused_by_drawdown)
    {
        trading_paused_by_drawdown = true;
        drawdown_pause_start = TimeCurrent();

        Print("🚨 警告：账户净值回撤达到", DoubleToString(current_drawdown_percent, 2), "%");
        Print("🛑 触发30%峰值回撤保护，交易已暂停");
        Print("📊 净值峰值：", DoubleToString(equity_peak, 2), " USD");
        Print("📊 当前净值：", DoubleToString(current_equity, 2), " USD");
        Print("⚠️  需要人工解除或等待净值创新高才能恢复交易");

        // 发送邮件或推送通知（如果配置了的话）
        SendNotification("EA交易暂停：净值回撤超过30%");
    }

    return trading_paused_by_drawdown;
}

//+------------------------------------------------------------------+
//| 检查波动异常                                                      |
//+------------------------------------------------------------------+
bool IsVolatilitySpike()
{
    // 获取当前M1 K线的波动范围
    double current_high = iHigh(Symbol(), PERIOD_M1, 1);
    double current_low = iLow(Symbol(), PERIOD_M1, 1);
    double current_range = current_high - current_low;
    
    // 计算过去10根K线的平均波动
    double total_range = 0;
    for(int i = 2; i <= 11; i++)
    {
        double high = iHigh(Symbol(), PERIOD_M1, i);
        double low = iLow(Symbol(), PERIOD_M1, i);
        total_range += (high - low);
    }
    double avg_range = total_range / 10.0;
    
    // 转换为点数进行比较
    double current_range_points = current_range / Point();
    double min_points_threshold = Spike_Min_Points;
    double max_points_threshold = Spike_Max_Points;

    // 修改说明：添加波动上限检查，避免极端波动下的反转失效
    if(current_range_points > max_points_threshold)
    {
        // 修改说明：添加调试信息，限制输出频率
        static datetime last_max_debug_time = 0;
        if(TimeCurrent() - last_max_debug_time >= 60) // 每分钟最多输出一次
        {
            last_max_debug_time = TimeCurrent();
            Print("⚠️ 波动过大跳过：当前波动 ", (int)current_range_points, " 点 > 上限 ", (int)max_points_threshold, " 点，避免极端市场下反转失效");
        }
        return false; // 波动过大，跳过信号
    }

    bool spike_detected = (current_range_points > min_points_threshold) &&
                         (current_range > avg_range * Spike_Range_Multiplier);
    
    // 修改说明：添加调试信息，限制输出频率
    static datetime last_debug_time = 0;
    if(TimeCurrent() - last_debug_time >= 60) // 每分钟最多输出一次
    {
        last_debug_time = TimeCurrent();
        if(spike_detected)
        {
            Print("🔥 波动异常检测成功：当前波动 ", (int)current_range_points, " 点，平均波动 ",
                  (int)(avg_range/Point()), " 点，倍数 ", DoubleToString(current_range/avg_range, 2));
        }
        else
        {
            Print("📊 波动检测：当前 ", (int)current_range_points, " 点 (需要>",
                  (int)min_points_threshold, " 点且>",
                  (int)(avg_range/Point() * Spike_Range_Multiplier), " 点，<",
                  (int)max_points_threshold, " 点)");
        }
    }

    return spike_detected;
}

//+------------------------------------------------------------------+
//| RSI过滤检查                                                       |
//+------------------------------------------------------------------+
bool IsRSIFilterPassed(string direction)
{
    if(!rsi_filter_active || rsi_handle == INVALID_HANDLE) return true;

    double rsi_buffer[];
    ArraySetAsSeries(rsi_buffer, true);

    // 获取RSI值
    if(CopyBuffer(rsi_handle, 0, 1, 1, rsi_buffer) <= 0)
    {
        Print("❌ RSI数据获取失败");
        return true; // 如果数据获取失败，不过滤信号
    }

    double rsi_value = rsi_buffer[0];

    // 修改说明：RSI阈值设置为50/50，使用中性的RSI过滤逻辑

    if(Use_Reversal_Strategy)
    {
        // 顺势策略的RSI过滤逻辑
        if(direction == "BUY")
        {
            // 顺势做多：希望RSI在超卖区域（确认超卖反弹后顺势）
            bool rsi_condition = (rsi_value < RSI_Oversold);

            // 修改说明：当RSI在30-40区间时，需要额外的波动条件（>180点）
            if(rsi_value >= 30 && rsi_value <= 40)
            {
                // 获取当前波动
                double current_high = iHigh(Symbol(), PERIOD_M1, 1);
                double current_low = iLow(Symbol(), PERIOD_M1, 1);
                double current_volatility = (current_high - current_low) / Point();

                bool volatility_condition = (current_volatility > 180);
                bool passed = rsi_condition && volatility_condition;

                if(!passed)
                {
                    if(!rsi_condition)
                        Print("🔍 RSI过滤：反转做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (未超卖)");
                    else if(!volatility_condition)
                        Print("🔍 RSI过滤：反转做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (30-40区间需要波动>180点，当前", DoubleToString(current_volatility, 1), "点)");
                }
                return passed;
            }
            else
            {
                // RSI不在30-40区间，使用原有逻辑
                if(!rsi_condition) Print("🔍 RSI过滤：反转做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (未超卖)");
                return rsi_condition;
            }
        }
        else // SELL
        {
            // 反转做空：希望RSI在超买区域（55-85区间内确认超买回调）
            bool passed = (rsi_value >= RSI_Overbought && rsi_value <= RSI_Overbought_Upper);
            if(!passed) Print("🔍 RSI过滤：反转做空信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (不在超买区间55-85)");
            return passed;
        }
    }
    else
    {
        // 原始顺势策略的RSI过滤逻辑
        if(direction == "BUY")
        {
            // 做多时，RSI应该不在超买区域（不在55-85区间）
            bool passed = !(rsi_value >= RSI_Overbought && rsi_value <= RSI_Overbought_Upper);
            if(!passed) Print("🔍 RSI过滤：做多信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (在超买区间55-85)");
            return passed;
        }
        else // SELL
        {
            // 做空时，RSI应该不在超卖区域
            bool passed = (rsi_value > RSI_Oversold);
            if(!passed) Print("🔍 RSI过滤：做空信号被过滤，RSI=", DoubleToString(rsi_value, 2), " (超卖)");
            return passed;
        }
    }
}

//+------------------------------------------------------------------+
//| EMA趋势过滤检查                                                   |
//+------------------------------------------------------------------+
bool IsEMATrendFilterPassed(string direction)
{
    if(!ema_filter_active || ema_fast_handle == INVALID_HANDLE || ema_slow_handle == INVALID_HANDLE)
        return true;

    double ema_fast[], ema_slow[];
    ArraySetAsSeries(ema_fast, true);
    ArraySetAsSeries(ema_slow, true);

    // 获取EMA值
    if(CopyBuffer(ema_fast_handle, 0, 1, 2, ema_fast) <= 0 ||
       CopyBuffer(ema_slow_handle, 0, 1, 2, ema_slow) <= 0)
    {
        Print("❌ EMA数据获取失败");
        return true;
    }

    // 当前EMA关系
    bool fast_above_slow_now = ema_fast[0] > ema_slow[0];
    bool fast_above_slow_prev = ema_fast[1] > ema_slow[1];

    if(Use_Reversal_Strategy)
    {
        // 修改说明：US30顺势策略的EMA过滤条件
        if(direction == "BUY")
        {
            // 顺势做多：价格在慢线附近或快线开始向上（顺势确认）
            double price = iClose(Symbol(), PERIOD_M1, 1);
            bool near_slow_ema = MathAbs(price - ema_slow[0]) / ema_slow[0] < 0.002; // 价格接近慢线（0.2%以内）
            bool fast_trending_up = ema_fast[0] > ema_fast[1]; // 快线向上

            bool condition_met = near_slow_ema || fast_trending_up || !fast_above_slow_now;
            if(!condition_met)
                Print("🔍 EMA过滤：反转做多被过滤，快线=", ema_fast[0], " 慢线=", ema_slow[0]);
            return condition_met;
        }
        else // SELL
        {
            // 反转做空：价格在慢线附近或快线开始向下
            double price = iClose(Symbol(), PERIOD_M1, 1);
            bool near_slow_ema = MathAbs(price - ema_slow[0]) / ema_slow[0] < 0.002; // 价格接近慢线（0.2%以内）
            bool fast_trending_down = ema_fast[0] < ema_fast[1]; // 快线向下

            bool condition_met = near_slow_ema || fast_trending_down || fast_above_slow_now;
            if(!condition_met)
                Print("🔍 EMA过滤：反转做空被过滤，快线=", ema_fast[0], " 慢线=", ema_slow[0]);
            return condition_met;
        }
    }
    else
    {
        // 顺势策略：跟随趋势方向
        if(direction == "BUY")
        {
            bool passed = fast_above_slow_now;
            if(!passed) Print("🔍 EMA过滤：做多被过滤，快线低于慢线");
            return passed;
        }
        else // SELL
        {
            bool passed = !fast_above_slow_now;
            if(!passed) Print("🔍 EMA过滤：做空被过滤，快线高于慢线");
            return passed;
        }
    }
}

//+------------------------------------------------------------------+
//| 检查成交量异常                                                    |
//+------------------------------------------------------------------+
bool IsVolumeSpike()
{
    // 获取当前M1 K线的成交量
    long current_volume = iVolume(Symbol(), PERIOD_M1, 1);
    
    // 计算过去Volume_MA_Period根K线的平均成交量
    long total_volume = 0;
    for(int i = 2; i <= Volume_MA_Period + 1; i++)
    {
        total_volume += iVolume(Symbol(), PERIOD_M1, i);
    }
    double avg_volume = (double)total_volume / Volume_MA_Period;
    
    bool volume_spike = current_volume > avg_volume * Volume_Threshold_Mult;
    
    if(volume_spike)
    {
        Print("成交量异常检测成功：当前成交量 ", current_volume, "，平均成交量 ", 
              (long)avg_volume, "，倍数 ", DoubleToString(current_volume/avg_volume, 2));
    }
    
    return volume_spike;
}

//+------------------------------------------------------------------+
//| 获取K线方向                                                       |
//+------------------------------------------------------------------+
string GetBarDirection()
{
    double open_price = iOpen(Symbol(), PERIOD_M1, 1);
    double close_price = iClose(Symbol(), PERIOD_M1, 1);

    if(Use_Reversal_Strategy)
    {
        // 修改说明：顺势策略 - 阳线做多，阴线做空（跟随趋势）
        return (close_price > open_price) ? "BUY" : "SELL";
    }
    else
    {
        // 修改说明：顺势策略 - 阳线做多，阴线做空（跟随趋势）
        // 当前设置：主策略为顺势，但亏损后仍执行反向交易
        return (close_price > open_price) ? "BUY" : "SELL";
    }
}

//+------------------------------------------------------------------+
//| 计算交易手数                                                      |
//+------------------------------------------------------------------+
double CalculateLotSize(int custom_stop_loss = 0)
{
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double risk_amount = balance * Risk_Percent_Per_Trade / 100.0;
    double tick_value = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);

    // 修改说明：根据交易类型使用不同的止损点位
    int actual_stop_loss = (custom_stop_loss > 0) ? custom_stop_loss : StopLoss_Points;
    double stop_loss_money = actual_stop_loss * tick_value;

    if(stop_loss_money <= 0) return 0;

    double lots = risk_amount / stop_loss_money;

    // 标准化手数
    double min_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double max_lot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lot_step = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    lots = MathMax(min_lot, MathMin(max_lot, lots));
    lots = NormalizeDouble(lots / lot_step, 0) * lot_step;

    return lots;
}

//+------------------------------------------------------------------+
//| 开仓交易                                                          |
//+------------------------------------------------------------------+
void OpenTrade(string direction, double lots)
{
    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = Symbol();
    request.volume = lots;
    request.deviation = Max_Slippage;
    request.magic = 12345;

    // 修改说明：根据实际测试结果，强制使用IOC模式
    // IOC模式具有最佳的兼容性和执行效率
    // 避免不同品种的成交模式兼容性问题
    request.type_filling = ORDER_FILLING_IOC;

    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

    // 修改说明：统一使用标准止损点位（已移除反向交易功能）
    int actual_stop_loss = StopLoss_Points;

    // 修改说明：添加止损止盈水平检查，确保符合经纪商要求
    int stops_level = (int)SymbolInfoInteger(Symbol(), SYMBOL_TRADE_STOPS_LEVEL);
    double min_stop_distance = stops_level * point;

    if(direction == "BUY")
    {
        request.type = ORDER_TYPE_BUY;
        request.price = current_ask;

        // 确保止损止盈距离符合最小要求
        double sl_distance = MathMax(actual_stop_loss * point, min_stop_distance);
        double tp_distance = MathMax(TakeProfit_Points * point, min_stop_distance);

        request.sl = NormalizeDouble(current_ask - sl_distance, Digits());
        request.tp = NormalizeDouble(current_ask + tp_distance, Digits());
    }
    else
    {
        request.type = ORDER_TYPE_SELL;
        request.price = current_bid;

        // 确保止损止盈距离符合最小要求
        double sl_distance = MathMax(actual_stop_loss * point, min_stop_distance);
        double tp_distance = MathMax(TakeProfit_Points * point, min_stop_distance);

        request.sl = NormalizeDouble(current_bid + sl_distance, Digits());
        request.tp = NormalizeDouble(current_bid - tp_distance, Digits());
    }

    // 修改说明：快速开仓优化 - 移除开仓前的Print输出，避免延迟
    // 开仓前不输出日志，直接发送交易请求以最小化延迟

    // 发送交易请求
    if(OrderSend(request, result))
    {
        if(result.retcode == TRADE_RETCODE_DONE)
        {
            // 修改说明：只有成功交易才增加计数器和更新统计
            daily_trade_count++;
            stats.total_trades++;

            if(stats.first_trade_time == 0)
                stats.first_trade_time = TimeCurrent();
            stats.last_trade_time = TimeCurrent();

            // 修改说明：快速开仓优化 - 开仓成功后输出详细信息（不影响开仓速度）
            Print("⚡ 快速开仓成功：", direction, " ", lots, " 手 @ ", result.price);
            Print("📊 交易详情：SL=", request.sl, " (", actual_stop_loss, "点), TP=", request.tp, ", 订单号=", result.order);
            Print("📈 成交模式：", EnumToString(request.type_filling), ", 最小止损距离：", stops_level, " 点");
            Print("📊 当日统计：", daily_trade_count, "/", Max_Trades_Per_Day, " 次 (总交易:", stats.total_trades, ")");

            // 修改说明：已移除反向交易相关记录
            // last_trade_direction = direction;     // 已移除
            // last_trade_was_loss = false;          // 已移除

            // 修改说明：新开仓时重置移动止损状态变量
            // 所有交易都使用相同的直接移动止损逻辑
            current_position_ticket = result.order; // 记录当前持仓票号

            // 修改说明：已移除保本保护变量初始化
            // if(Breakeven_Protection)  // 已移除保本保护功能
            // {
            //     breakeven_activated = false;
            //     position_ticket = result.order;
            //     if(direction == "BUY")
            //         position_open_price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
            //     else
            //         position_open_price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
            //     Print("🛡️ 保本保护已启用，开仓价格：", DoubleToString(position_open_price, 5),
            //           "，触发点数：", Breakeven_Trigger_Points, "点");
            // }

            // 修改说明：等待交易完成后统计盈亏结果
            CheckTradeResult(result.order, direction, lots);
        }
        else
        {
            // 修改说明：快速开仓优化 - 开仓失败后输出详细错误信息
            Print("❌ 交易失败：错误代码 ", result.retcode, "，描述：", result.comment);
            Print("📊 失败详情：", direction, " ", lots, " 手，价格=", request.price, "，SL=", request.sl, "，TP=", request.tp);
            Print("📊 止损点位：", actual_stop_loss, " 点，成交模式：", EnumToString(request.type_filling));
        }
    }
    else
    {
        int error_code = GetLastError();
        Print("❌ 交易请求失败：错误代码 ", error_code, "，描述：", ErrorDescription(error_code));
        Print("📊 请求详情：", direction, " ", lots, " 手，价格=", request.price);
    }
}

//+------------------------------------------------------------------+
//| 获取错误描述                                                      |
//+------------------------------------------------------------------+
string ErrorDescription(int error_code)
{
    switch(error_code)
    {
        case 4756: return "不支持的成交模式";
        case 4752: return "订单被拒绝";
        case 4753: return "订单被取消";
        case 4754: return "订单已放置";
        case 4755: return "请求已完成";
        case 10004: return "交易被禁用";
        case 10006: return "请求被拒绝";
        case 10007: return "请求被取消";
        case 10008: return "订单已放置";
        case 10009: return "请求已完成";
        case 10010: return "仅允许多头交易";
        case 10011: return "仅允许空头交易";
        case 10012: return "仅允许平仓";
        case 10013: return "仅允许减仓";
        case 10014: return "禁止开仓";
        case 10015: return "自动交易被禁用";
        case 10016: return "市场关闭";
        case 10017: return "没有足够资金";
        case 10018: return "价格已改变";
        case 10019: return "经纪商忙碌";
        case 10020: return "重新报价";
        case 10021: return "订单锁定";
        case 10022: return "买单数量无效";
        case 10023: return "卖单数量无效";
        case 10024: return "订单数量无效";
        case 10025: return "订单价格无效";
        case 10026: return "止损无效";
        case 10027: return "止盈无效";
        default: return "未知错误";
    }
}

//+------------------------------------------------------------------+
//| 交易事件处理函数                                                  |
//+------------------------------------------------------------------+
void OnTrade()
{
    // 修改说明：已移除保本保护状态检查
    // if(!PositionExists())  // 已移除保本保护功能
    // {
    //     if(breakeven_activated || position_ticket != 0)
    //     {
    //         Print("🔄 持仓已关闭，重置保本保护状态");
    //         breakeven_activated = false;
    //         position_open_price = 0;
    //         position_ticket = 0;
    //     }
    // }

    // 修改说明：实盘模式下的交易事件处理和统计
    static double last_balance = 0;

    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);

    // 初始化
    if(last_balance == 0)
    {
        last_balance = current_balance;
        return;
    }

    // 检查余额变化
    double balance_change = current_balance - last_balance;

    // 修改说明：计算余额变化的百分比，避免手续费等微小变化被误判为交易结果
    double balance_change_percentage = (MathAbs(balance_change) / current_balance) * 100.0;
    if(balance_change_percentage > 0.1) // 余额变化超过0.1%才认为是交易结果
    {
        if(balance_change > 0) // 盈利交易
        {
            stats.winning_trades++;
            stats.total_profit += balance_change;
            stats.consecutive_wins++;
            stats.consecutive_losses = 0;

            if(balance_change > stats.max_profit)
                stats.max_profit = balance_change;

            if(stats.consecutive_wins > stats.max_consecutive_wins)
                stats.max_consecutive_wins = stats.consecutive_wins;

            Print("✅ 盈利交易完成：+", DoubleToString(balance_change, 2), " USD");

            // 修改说明：发送盈利交易电报通知（不影响正常交易）
            if(Telegram_Enabled && Telegram_Trade_Alerts && telegram_initialized)
            {
                SendTradeTelegramNotification(true, balance_change, balance_change, 0);
            }

            // 修改说明：已移除反向交易相关逻辑
            // consecutive_reverse_count = 0;        // 已移除
            // reverse_trade_pending = false;        // 已移除
            // is_reverse_trade = false;             // 已移除
            Print("✅ 盈利交易完成");
        }
        else // 亏损交易
        {
            // 修改说明：对于亏损交易，使用已计算的百分比判断是否为小幅亏损
            bool is_minor_loss = balance_change_percentage <= 0.5; // 0.5%以内视为小幅亏损（手续费等造成）

            stats.losing_trades++;
            stats.total_loss += MathAbs(balance_change);
            stats.consecutive_losses++;
            stats.consecutive_wins = 0;

            if(MathAbs(balance_change) > stats.max_loss)
                stats.max_loss = MathAbs(balance_change);

            if(stats.consecutive_losses > stats.max_consecutive_losses)
                stats.max_consecutive_losses = stats.consecutive_losses;

            Print("❌ 亏损交易完成：", DoubleToString(balance_change, 2), " USD (", DoubleToString(balance_change_percentage, 3), "% 账户余额)");

            // 修改说明：发送亏损交易电报通知（不影响正常交易）
            if(Telegram_Enabled && Telegram_Trade_Alerts && telegram_initialized)
            {
                SendTradeTelegramNotification(false, balance_change, balance_change, 0);
            }

            // 修改说明：已移除亏损后反向交易逻辑
            // last_trade_was_loss = true;           // 已移除
            // last_loss_time = TimeCurrent();       // 已移除

            // 修改说明：检查是否为小幅亏损（仅用于统计）
            if(is_minor_loss)
            {
                Print("🔸 小幅亏损 (≤0.5%)：可能由手续费/滑点/点差造成");
                return; // 直接返回
            }

            Print("❌ 亏损交易：已移除反向交易功能，等待下一个正常信号");
        }

        last_balance = current_balance;
    }

    // 更新最大余额和回撤
    if(current_balance > stats.max_balance)
        stats.max_balance = current_balance;

    double current_drawdown = (stats.max_balance - current_balance) / stats.max_balance * 100;
    if(current_drawdown > stats.max_drawdown)
        stats.max_drawdown = current_drawdown;

    // 修改说明：在每笔交易完成后检查30%峰值回撤保护
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    if(current_equity > equity_peak)
    {
        equity_peak = current_equity;
        Print("📈 净值创新高：", DoubleToString(equity_peak, 2), " USD");
    }
}

//+------------------------------------------------------------------+
//| 检查交易结果（用于统计）                                          |
//+------------------------------------------------------------------+
void CheckTradeResult(ulong order_ticket, string direction, double lots)
{
    // 修改说明：通过定期检查持仓状态来统计交易结果，避免OnTrade()的重复统计问题
    // 这个函数暂时不实现复杂逻辑，改为在每次Tick时检查账户历史
    // 实际的盈亏统计将通过账户余额变化来计算
}

//+------------------------------------------------------------------+
//| 获取精确的交易统计（排除手续费影响）                                |
//+------------------------------------------------------------------+
void GetAccurateTradeStatistics(int &wins, int &losses, double &total_profit, double &total_loss, double &max_profit, double &max_loss)
{
    // 初始化统计变量
    wins = 0;
    losses = 0;
    total_profit = 0;
    total_loss = 0;
    max_profit = 0;
    max_loss = 0;

    // 选择历史交易
    if(!HistorySelect(0, TimeCurrent()))
        return;

    int total_deals = HistoryDealsTotal();
    if(total_deals <= 0)
        return;

    // 使用结构体来跟踪每个持仓的总盈亏
    struct PositionSummary
    {
        ulong position_id;
        ENUM_POSITION_TYPE position_type;
        double total_profit;
        bool is_processed;
    };

    PositionSummary positions[];
    ArrayResize(positions, 0);

    // 第一步：收集所有持仓的信息
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0)
            continue;

        // 只统计本EA的交易
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != 12345)
            continue;

        // 只统计本品种的交易
        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != Symbol())
            continue;

        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        ulong position_id = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

        // 跳过开仓交易（通常盈亏为0）
        if(MathAbs(deal_profit) < 0.01)
            continue;

        // 查找或创建持仓记录
        int pos_index = -1;
        for(int j = 0; j < ArraySize(positions); j++)
        {
            if(positions[j].position_id == position_id)
            {
                pos_index = j;
                break;
            }
        }

        if(pos_index == -1)
        {
            // 创建新的持仓记录
            ArrayResize(positions, ArraySize(positions) + 1);
            pos_index = ArraySize(positions) - 1;
            positions[pos_index].position_id = position_id;
            positions[pos_index].total_profit = 0;
            positions[pos_index].is_processed = false;

            // 确定持仓类型
            if(deal_type == DEAL_TYPE_SELL)
                positions[pos_index].position_type = POSITION_TYPE_BUY;
            else if(deal_type == DEAL_TYPE_BUY)
                positions[pos_index].position_type = POSITION_TYPE_SELL;
        }

        // 累加盈亏
        positions[pos_index].total_profit += deal_profit;
    }

    // 第二步：统计每个持仓的结果
    for(int i = 0; i < ArraySize(positions); i++)
    {
        double position_profit = positions[i].total_profit;

        // 只统计有意义的盈亏（排除手续费等小额变化）
        if(position_profit > 0.5) // 盈利超过0.5美元
        {
            wins++;
            total_profit += position_profit;

            if(position_profit > max_profit)
                max_profit = position_profit;
        }
        else if(position_profit < -0.5) // 亏损超过0.5美元
        {
            losses++;
            total_loss += MathAbs(position_profit);

            if(MathAbs(position_profit) > max_loss)
                max_loss = MathAbs(position_profit);
        }
        // 忽略±0.5美元内的微小变化（可能是手续费）
    }
}

//+------------------------------------------------------------------+
//| 更新交易统计（修复版本）                                          |
//+------------------------------------------------------------------+
void UpdateTradeStatistics()
{
    // 修改说明：实盘模式使用简化的实时统计更新

    // 更新最大余额和回撤
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(current_balance > stats.max_balance)
        stats.max_balance = current_balance;

    double current_drawdown = (stats.max_balance - current_balance) / stats.max_balance * 100;
    if(current_drawdown > stats.max_drawdown)
        stats.max_drawdown = current_drawdown;
}

//+------------------------------------------------------------------+
//| 修复分批止盈问题的交易统计分析                                    |
//+------------------------------------------------------------------+
void UpdateTradeStatsManually()
{
    // 修改说明：使用持仓ID来正确处理分批止盈，避免重复统计
    if(!HistorySelect(0, TimeCurrent())) return;

    int total_deals = HistoryDealsTotal();
    if(total_deals <= 0) return;

    Print("🔍 调试：开始分析 ", total_deals, " 个历史成交");

    // 重置所有统计数据
    stats.winning_trades = 0;
    stats.losing_trades = 0;
    stats.total_profit = 0;
    stats.total_loss = 0;
    stats.max_profit = 0;
    stats.max_loss = 0;
    stats.consecutive_wins = 0;
    stats.consecutive_losses = 0;
    stats.max_consecutive_wins = 0;
    stats.max_consecutive_losses = 0;

    // 重置做多做空统计
    stats.buy_total = 0;
    stats.buy_wins = 0;
    stats.buy_losses = 0;
    stats.buy_profit = 0;
    stats.buy_loss = 0;
    stats.buy_max_profit = 0;
    stats.buy_max_loss = 0;

    stats.sell_total = 0;
    stats.sell_wins = 0;
    stats.sell_losses = 0;
    stats.sell_profit = 0;
    stats.sell_loss = 0;
    stats.sell_max_profit = 0;
    stats.sell_max_loss = 0;

    // 使用结构体来跟踪每个持仓的总盈亏
    struct PositionSummary
    {
        ulong position_id;
        ENUM_POSITION_TYPE position_type;
        double total_profit;
        bool is_processed;
    };

    PositionSummary positions[];
    ArrayResize(positions, 0);

    // 第一步：收集所有持仓的信息
    for(int i = 0; i < total_deals; i++)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0) continue;

        if(HistoryDealGetString(deal_ticket, DEAL_SYMBOL) != Symbol()) continue;
        if(HistoryDealGetInteger(deal_ticket, DEAL_MAGIC) != 12345) continue;

        ENUM_DEAL_TYPE deal_type = (ENUM_DEAL_TYPE)HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        double deal_profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        ulong position_id = HistoryDealGetInteger(deal_ticket, DEAL_POSITION_ID);

        // 跳过开仓交易（通常盈亏为0）
        if(MathAbs(deal_profit) < 0.01) continue;

        // 查找或创建持仓记录
        int pos_index = -1;
        for(int j = 0; j < ArraySize(positions); j++)
        {
            if(positions[j].position_id == position_id)
            {
                pos_index = j;
                break;
            }
        }

        if(pos_index == -1)
        {
            // 创建新的持仓记录
            ArrayResize(positions, ArraySize(positions) + 1);
            pos_index = ArraySize(positions) - 1;
            positions[pos_index].position_id = position_id;
            positions[pos_index].total_profit = 0;
            positions[pos_index].is_processed = false;

            // 确定持仓类型
            if(deal_type == DEAL_TYPE_SELL)
                positions[pos_index].position_type = POSITION_TYPE_BUY;
            else if(deal_type == DEAL_TYPE_BUY)
                positions[pos_index].position_type = POSITION_TYPE_SELL;
        }

        // 累加盈亏
        positions[pos_index].total_profit += deal_profit;
    }

    Print("🔍 调试：找到 ", ArraySize(positions), " 个独立持仓");

    // 第二步：统计每个持仓的结果
    int current_consecutive_wins = 0;
    int current_consecutive_losses = 0;

    for(int i = 0; i < ArraySize(positions); i++)
    {
        double position_profit = positions[i].total_profit;
        ENUM_POSITION_TYPE position_type = positions[i].position_type;

        // 总体统计
        double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
        double profit_percentage = (MathAbs(position_profit) / current_balance) * 100.0;

        if(position_profit > 0) // 盈利交易，不设置百分比限制
        {
            stats.winning_trades++;
            stats.total_profit += position_profit;
            current_consecutive_wins++;
            current_consecutive_losses = 0;

            if(position_profit > stats.max_profit)
                stats.max_profit = position_profit;

            if(current_consecutive_wins > stats.max_consecutive_wins)
                stats.max_consecutive_wins = current_consecutive_wins;
        }
        else if(position_profit < 0 && profit_percentage > 0.5) // 修改说明：亏损交易使用0.5%阈值，避免手续费等微小变化被误判
        {
            stats.losing_trades++;
            stats.total_loss += MathAbs(position_profit);
            current_consecutive_losses++;
            current_consecutive_wins = 0;

            if(MathAbs(position_profit) > stats.max_loss)
                stats.max_loss = MathAbs(position_profit);

            if(current_consecutive_losses > stats.max_consecutive_losses)
                stats.max_consecutive_losses = current_consecutive_losses;
        }

        // 做多做空分别统计
        if(position_type == POSITION_TYPE_BUY) // 做多交易
        {
            stats.buy_total++;
            if(position_profit > 0) // 盈利交易，不设置百分比限制
            {
                stats.buy_wins++;
                stats.buy_profit += position_profit;
                if(position_profit > stats.buy_max_profit)
                    stats.buy_max_profit = position_profit;
            }
            else if(position_profit < 0 && profit_percentage > 0.5) // 亏损交易使用0.5%阈值
            {
                stats.buy_losses++;
                stats.buy_loss += MathAbs(position_profit);
                if(MathAbs(position_profit) > stats.buy_max_loss)
                    stats.buy_max_loss = MathAbs(position_profit);
            }
        }
        else if(position_type == POSITION_TYPE_SELL) // 做空交易
        {
            stats.sell_total++;
            if(position_profit > 0) // 盈利交易，不设置百分比限制
            {
                stats.sell_wins++;
                stats.sell_profit += position_profit;
                if(position_profit > stats.sell_max_profit)
                    stats.sell_max_profit = position_profit;
            }
            else if(position_profit < 0 && profit_percentage > 0.5) // 亏损交易使用0.5%阈值
            {
                stats.sell_losses++;
                stats.sell_loss += MathAbs(position_profit);
                if(MathAbs(position_profit) > stats.sell_max_loss)
                    stats.sell_max_loss = MathAbs(position_profit);
            }
        }
    }

    // 更新当前连续状态
    stats.consecutive_wins = current_consecutive_wins;
    stats.consecutive_losses = current_consecutive_losses;

    // 更新总交易次数
    stats.total_trades = stats.winning_trades + stats.losing_trades;

    Print("🔍 调试：统计完成 - 做多:", stats.buy_total, " 做空:", stats.sell_total, " 总计:", stats.total_trades);
    Print("🔍 调试：盈利:", stats.winning_trades, " 亏损:", stats.losing_trades);
}

//+------------------------------------------------------------------+
//| 检查并打印周期性报告                                              |
//+------------------------------------------------------------------+
void CheckAndPrintPeriodicReport()
{
    // 修改说明：每6小时输出一次统计报告
    if(TimeCurrent() - last_report_time >= 21600) // 6小时 = 21600秒
    {
        PrintPeriodicReport();
        last_report_time = TimeCurrent();
    }
}

//+------------------------------------------------------------------+
//| 打印周期性统计报告                                                |
//+------------------------------------------------------------------+
void PrintPeriodicReport()
{
    // 修改说明：实盘模式使用实时统计，不重新计算历史数据

    Print("==================== 6小时统计报告 ====================");
    Print("报告时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("当前余额：", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2), " USD");
    Print("今日交易：", daily_trade_count, "/", Max_Trades_Per_Day, " (尝试:", daily_trade_attempts, ")");

    // 修改说明：修复胜率计算，确保逻辑正确
    int completed_trades = stats.winning_trades + stats.losing_trades;
    if(completed_trades > 0)
    {
        double win_rate = (double)stats.winning_trades / completed_trades * 100;
        double avg_profit = stats.winning_trades > 0 ? stats.total_profit / stats.winning_trades : 0;
        double avg_loss = stats.losing_trades > 0 ? stats.total_loss / stats.losing_trades : 0;
        double profit_factor = stats.total_loss > 0 ? stats.total_profit / stats.total_loss : 0;

        Print("已完成交易：", completed_trades, " (开仓:", stats.total_trades, ")");
        Print("胜率：", DoubleToString(win_rate, 2), "% (", stats.winning_trades, "/", completed_trades, ")");
        Print("盈亏比：", DoubleToString(profit_factor, 2));
        Print("平均盈利：", DoubleToString(avg_profit, 2), " USD");
        Print("平均亏损：", DoubleToString(avg_loss, 2), " USD");
        Print("最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    }
    else
    {
        Print("已完成交易：0 (开仓:", stats.total_trades, ")");
        Print("胜率：0.00% (无已完成交易)");
    }

    Print("信号统计：波动信号", stats.volatility_signals, "，成交量信号", stats.volume_signals, "，过滤信号", stats.filtered_signals);
    Print("风险控制：日亏损限制触发次数", daily_loss_limit_hit_count, "次");
    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 打印最终统计报告                                                  |
//+------------------------------------------------------------------+
void PrintFinalReport()
{
    Print("==================== US30道指专用EA最终报告 ====================");
    Print("回测结束时间：", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
    Print("交易品种：", Symbol(), " (US30道指专用优化)");
    Print("EA版本：v1.0 - US30 TrendFollowing Professional (专业版)");
    Print("");

    // 基本统计
    Print("【基本统计】");
    Print("总开仓次数：", stats.total_trades);
    Print("盈利交易：", stats.winning_trades);
    Print("亏损交易：", stats.losing_trades);

    // 修改说明：修复胜率计算逻辑
    int completed_trades = stats.winning_trades + stats.losing_trades;
    Print("已完成交易：", completed_trades);

    if(completed_trades > 0)
    {
        double win_rate = (double)stats.winning_trades / completed_trades * 100;
        Print("胜率：", DoubleToString(win_rate, 2), "% (", stats.winning_trades, "/", completed_trades, ")");
    }
    else
    {
        Print("胜率：0.00% (无已完成交易)");
    }

    // 盈亏统计
    Print("");
    Print("【盈亏统计】");
    double net_profit = stats.total_profit - stats.total_loss;
    Print("总盈利：", DoubleToString(stats.total_profit, 2), " USD");
    Print("总亏损：", DoubleToString(stats.total_loss, 2), " USD");
    Print("净盈利：", DoubleToString(net_profit, 2), " USD");

    if(stats.total_loss > 0)
    {
        double profit_factor = stats.total_profit / stats.total_loss;
        Print("盈亏比：", DoubleToString(profit_factor, 2));
    }
    else
    {
        Print("盈亏比：", stats.total_profit > 0 ? "∞" : "0.00");
    }

    Print("最大单笔盈利：", DoubleToString(stats.max_profit, 2), " USD");
    Print("最大单笔亏损：", DoubleToString(stats.max_loss, 2), " USD");

    // 做多做空详细分析
    Print("");
    Print("【做多交易分析】");
    double buy_win_rate = 0;
    if(stats.buy_total > 0)
        buy_win_rate = (double)stats.buy_wins / stats.buy_total * 100;
    Print("做多总次数：", stats.buy_total);
    Print("做多盈利：", stats.buy_wins, " 次");
    Print("做多亏损：", stats.buy_losses, " 次");
    Print("做多胜率：", DoubleToString(buy_win_rate, 2), "% (", stats.buy_wins, "/", stats.buy_total, ")");
    Print("做多总盈利：", DoubleToString(stats.buy_profit, 2), " USD");
    Print("做多总亏损：", DoubleToString(stats.buy_loss, 2), " USD");
    Print("做多净盈利：", DoubleToString(stats.buy_profit - stats.buy_loss, 2), " USD");
    double buy_profit_factor = (stats.buy_loss > 0) ? stats.buy_profit / stats.buy_loss : 0;
    Print("做多盈亏比：", DoubleToString(buy_profit_factor, 2));
    Print("做多最大盈利：", DoubleToString(stats.buy_max_profit, 2), " USD");
    Print("做多最大亏损：", DoubleToString(stats.buy_max_loss, 2), " USD");

    Print("");
    Print("【做空交易分析】");
    double sell_win_rate = 0;
    if(stats.sell_total > 0)
        sell_win_rate = (double)stats.sell_wins / stats.sell_total * 100;
    Print("做空总次数：", stats.sell_total);
    Print("做空盈利：", stats.sell_wins, " 次");
    Print("做空亏损：", stats.sell_losses, " 次");
    Print("做空胜率：", DoubleToString(sell_win_rate, 2), "% (", stats.sell_wins, "/", stats.sell_total, ")");
    Print("做空总盈利：", DoubleToString(stats.sell_profit, 2), " USD");
    Print("做空总亏损：", DoubleToString(stats.sell_loss, 2), " USD");
    Print("做空净盈利：", DoubleToString(stats.sell_profit - stats.sell_loss, 2), " USD");
    double sell_profit_factor = (stats.sell_loss > 0) ? stats.sell_profit / stats.sell_loss : 0;
    Print("做空盈亏比：", DoubleToString(sell_profit_factor, 2));
    Print("做空最大盈利：", DoubleToString(stats.sell_max_profit, 2), " USD");
    Print("做空最大亏损：", DoubleToString(stats.sell_max_loss, 2), " USD");

    // 数据一致性验证
    Print("");
    Print("【数据一致性验证】");
    int calculated_total = stats.buy_total + stats.sell_total;
    int calculated_wins = stats.buy_wins + stats.sell_wins;
    int calculated_losses = stats.buy_losses + stats.sell_losses;
    double calculated_profit = stats.buy_profit + stats.sell_profit;
    double calculated_loss = stats.buy_loss + stats.sell_loss;

    Print("做多+做空总数：", calculated_total, " (应等于完成交易数：", completed_trades, ")");
    Print("做多+做空盈利：", calculated_wins, " (应等于总盈利：", stats.winning_trades, ")");
    Print("做多+做空亏损：", calculated_losses, " (应等于总亏损：", stats.losing_trades, ")");
    Print("做多+做空总盈利：", DoubleToString(calculated_profit, 2), " (应等于：", DoubleToString(stats.total_profit, 2), ")");
    Print("做多+做空总亏损：", DoubleToString(calculated_loss, 2), " (应等于：", DoubleToString(stats.total_loss, 2), ")");

    // 标记不一致的数据
    if(calculated_total != completed_trades)
        Print("⚠️ 警告：做多做空总数不匹配！");
    if(calculated_wins != stats.winning_trades)
        Print("⚠️ 警告：盈利交易数不匹配！");
    if(calculated_losses != stats.losing_trades)
        Print("⚠️ 警告：亏损交易数不匹配！");
    if(MathAbs(calculated_profit - stats.total_profit) > 0.01)
        Print("⚠️ 警告：总盈利金额不匹配！");
    if(MathAbs(calculated_loss - stats.total_loss) > 0.01)
        Print("⚠️ 警告：总亏损金额不匹配！");

    // 连续交易统计
    Print("");
    Print("【连续交易统计】");
    Print("最大连续盈利：", stats.max_consecutive_wins, " 次");
    Print("最大连续亏损：", stats.max_consecutive_losses, " 次");
    Print("当前连续盈利：", stats.consecutive_wins, " 次");
    Print("当前连续亏损：", stats.consecutive_losses, " 次");

    // 风险统计
    Print("");
    Print("【风险统计】");
    Print("最大余额：", DoubleToString(stats.max_balance, 2), " USD");
    Print("最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    Print("当前余额：", DoubleToString(current_balance, 2), " USD");
    Print("日亏损限制触发次数：", daily_loss_limit_hit_count, " 次");
    // Print("连续反向交易限制：最多", max_reverse_trades, "次，当前", consecutive_reverse_count, "次 (过滤≤0.5%小幅亏损)");  // 已移除反向交易功能

    // 修改说明：修复总收益率计算，使用初始余额而不是当日开始余额
    if(initial_balance > 0)
    {
        double total_return = (current_balance - initial_balance) / initial_balance * 100;
        Print("总收益率：", DoubleToString(total_return, 2), "%");
        Print("初始余额：", DoubleToString(initial_balance, 2), " USD");
    }

    // 信号统计
    Print("");
    Print("【信号统计】");
    Print("波动异常信号：", stats.volatility_signals);
    Print("成交量异常信号：", stats.volume_signals);
    Print("被过滤信号：", stats.filtered_signals);
    // 修改说明：使用精确统计计算信号转换率（排除手续费影响）
    int accurate_wins = 0, accurate_losses = 0;
    double temp_profit = 0, temp_loss = 0, temp_max_profit = 0, temp_max_loss = 0;
    GetAccurateTradeStatistics(accurate_wins, accurate_losses, temp_profit, temp_loss, temp_max_profit, temp_max_loss);

    int total_trades = accurate_wins + accurate_losses;
    int total_signals = total_trades + stats.filtered_signals;
    if(total_signals > 0)
    {
        double signal_efficiency = (double)total_trades / total_signals * 100;
        Print("信号转换率：", DoubleToString(signal_efficiency, 2), "% (", total_trades, "/", total_signals, ") [排除手续费统计]");
    }

    Print("");
    Print("【过滤效果分析】");
    if(stats.rsi_filtered > 0)
        Print("RSI过滤次数：", stats.rsi_filtered);
    if(stats.ema_filtered > 0)
        Print("EMA过滤次数：", stats.ema_filtered);
    if(stats.time_filtered > 0)
        Print("时间过滤次数：", stats.time_filtered);
    if(stats.daily_limit_filtered > 0)
        Print("日限制过滤次数：", stats.daily_limit_filtered);

    // 策略效果分析
    Print("");
    Print("【策略效果分析】");
    if(stats.buy_total > 0 && stats.sell_total > 0)
    {
        Print("做多做空比例：", DoubleToString((double)stats.buy_total/stats.sell_total, 2), ":1");
        Print("做多贡献度：", DoubleToString((stats.buy_profit - stats.buy_loss)/(stats.total_profit - stats.total_loss)*100, 2), "%");
        Print("做空贡献度：", DoubleToString((stats.sell_profit - stats.sell_loss)/(stats.total_profit - stats.total_loss)*100, 2), "%");
    }

    // 数学期望分析
    if(completed_trades > 0)
    {
        double win_rate_decimal = (double)stats.winning_trades / completed_trades;
        double profit_factor = (stats.total_loss > 0) ? stats.total_profit / stats.total_loss : 0;
        double mathematical_expectation = win_rate_decimal * profit_factor * 100;
        Print("数学期望：", DoubleToString(mathematical_expectation, 2), "%");

        if(mathematical_expectation > 100)
            Print("策略评级：优秀 (数学期望>100%)");
        else if(mathematical_expectation > 80)
            Print("策略评级：良好 (数学期望>80%)");
        else if(mathematical_expectation > 60)
            Print("策略评级：一般 (数学期望>60%)");
        else
            Print("策略评级：需要改进 (数学期望<60%)");
    }

    // 时间统计
    Print("");
    Print("【时间统计】");
    if(stats.first_trade_time > 0)
    {
        Print("首次交易：", TimeToString(stats.first_trade_time, TIME_DATE|TIME_MINUTES));
        Print("最后交易：", TimeToString(stats.last_trade_time, TIME_DATE|TIME_MINUTES));

        int trading_days = (int)((stats.last_trade_time - stats.first_trade_time) / 86400) + 1;
        if(trading_days > 0)
        {
            double trades_per_day = (double)stats.total_trades / trading_days;
            Print("交易天数：", trading_days, " 天");
            Print("日均交易：", DoubleToString(trades_per_day, 2), " 次");
        }
    }
    else
    {
        Print("无交易记录");
    }

    // 参数设置
    Print("");
    Print("【参数设置】");
    Print("风险百分比：", Risk_Percent_Per_Trade, "% (优化值：1.5%)");
    Print("波动倍数：", Spike_Range_Multiplier, " (优化值：1.3)");
    Print("波动范围：", Spike_Min_Points, "-", Spike_Max_Points, " 点 (优化值：115-600点，更严格避免极端波动)");
    Print("成交量过滤：", Volume_Spike_Enabled ? "启用" : "禁用", " (优化：启用)");
    Print("成交量倍数：", Volume_Threshold_Mult, " (优化值：1.05)");
    Print("止损：", StopLoss_Points, " 点 (优化值：125点)");
    Print("止盈：", TakeProfit_Points, " 点 (优化值：300点)");
    Print("移动止损：统一", Trailing_Stop_Distance_Buy, "点/", Trailing_Stop_Step_Buy, "步 (做多做空相同，无保本保护)");
    Print("RSI过滤：", RSI_Filter_Enabled ? "启用" : "禁用", " (优化：启用，30-40区间需波动>180点)");
    Print("EMA过滤：", EMA_Trend_Filter ? "启用" : "禁用", " (优化：启用)");
    Print("交易策略：", Use_Reversal_Strategy ? "反转策略" : "顺势策略", " (优化：顺势)");
    Print("日交易限制：", Max_Trades_Per_Day, " 次 (设置：30次)");
    Print("日亏损限制：", Max_Drawdown_Per_Day, "% (设置：2.5%)");

    Print("====================================================");
    Print("回测报告生成完成");
    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 定时账户报告                                                      |
//+------------------------------------------------------------------+
void PrintAccountReport()
{
    datetime current_time = TimeCurrent();
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double current_margin = AccountInfoDouble(ACCOUNT_MARGIN);
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);
    double margin_level = AccountInfoDouble(ACCOUNT_MARGIN_LEVEL);

    // 计算当日盈亏
    double daily_pnl = current_balance - daily_start_balance;
    double daily_pnl_percent = (daily_start_balance > 0) ? (daily_pnl / daily_start_balance * 100) : 0;

    // 计算总收益
    double total_return = (initial_balance > 0) ? ((current_balance - initial_balance) / initial_balance * 100) : 0;

    // 计算当前回撤
    double current_drawdown = (stats.max_balance > 0) ? ((stats.max_balance - current_balance) / stats.max_balance * 100) : 0;

    Print("==================== 账户状态报告 ====================");
    Print("📅 服务器时间：", TimeToString(current_time, TIME_DATE|TIME_MINUTES));
    Print("📅 本地时间：", TimeToString(TimeLocal(), TIME_DATE|TIME_MINUTES));
    Print("💰 当前余额：", DoubleToString(current_balance, 2), " USD");
    Print("💎 当前净值：", DoubleToString(current_equity, 2), " USD");
    Print("📊 初始余额：", DoubleToString(initial_balance, 2), " USD");
    Print("📈 总收益率：", DoubleToString(total_return, 2), "%");
    Print("📉 最大回撤：", DoubleToString(stats.max_drawdown, 2), "%");
    Print("📉 当前回撤：", DoubleToString(current_drawdown, 2), "%");

    // 修改说明：添加30%峰值回撤保护状态显示
    double equity_drawdown_percent = (equity_peak > 0) ? ((equity_peak - current_equity) / equity_peak * 100) : 0;
    Print("🛡️  净值峰值：", DoubleToString(equity_peak, 2), " USD");
    Print("🛡️  峰值回撤：", DoubleToString(equity_drawdown_percent, 2), "% / ", Max_Equity_Drawdown, "%");
    if(trading_paused_by_drawdown)
    {
        Print("🚨 回撤保护状态：已暂停交易（回撤超过30%）");
        if(drawdown_pause_start > 0)
            Print("⏰ 暂停开始时间：", TimeToString(drawdown_pause_start, TIME_DATE|TIME_MINUTES));
    }
    else
    {
        Print("✅ 回撤保护状态：正常交易");
    }

    Print("🔄 当日盈亏：", DoubleToString(daily_pnl, 2), " USD (", DoubleToString(daily_pnl_percent, 2), "%)");
    Print("🎯 今日交易：", daily_trade_count, " 次 / ", Max_Trades_Per_Day, " 次限制");

    // 保证金信息
    Print("💳 已用保证金：", DoubleToString(current_margin, 2), " USD");
    Print("💳 可用保证金：", DoubleToString(free_margin, 2), " USD");
    if(margin_level > 0)
        Print("💳 保证金水平：", DoubleToString(margin_level, 2), "%");

    // 持仓信息
    int total_positions = PositionsTotal();
    if(total_positions > 0)
    {
        Print("📍 当前持仓：", total_positions, " 个");
        for(int i = 0; i < total_positions; i++)
        {
            if(PositionGetTicket(i) > 0 && PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                string pos_type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? "做多" : "做空";
                double pos_volume = PositionGetDouble(POSITION_VOLUME);
                double pos_profit = PositionGetDouble(POSITION_PROFIT);
                Print("   ", pos_type, " ", DoubleToString(pos_volume, 2), " 手，浮盈：", DoubleToString(pos_profit, 2), " USD");
            }
        }
    }
    else
    {
        Print("📍 当前持仓：无");
    }

    // 策略表现统计（修改说明：使用交易历史精确统计，排除手续费影响）
    Print("================== 策略表现统计 ==================");

    // 获取精确的交易统计（排除手续费）
    int accurate_wins = 0;
    int accurate_losses = 0;
    double accurate_total_profit = 0;
    double accurate_total_loss = 0;
    double accurate_max_profit = 0;
    double accurate_max_loss = 0;

    GetAccurateTradeStatistics(accurate_wins, accurate_losses, accurate_total_profit, accurate_total_loss, accurate_max_profit, accurate_max_loss);

    int total_completed = accurate_wins + accurate_losses;
    if(total_completed > 0)
    {
        double win_rate = (double)accurate_wins / total_completed * 100;
        double profit_factor = (accurate_total_loss > 0) ? (accurate_total_profit / accurate_total_loss) : 0;
        double avg_win = (accurate_wins > 0) ? (accurate_total_profit / accurate_wins) : 0;
        double avg_loss = (accurate_losses > 0) ? (accurate_total_loss / accurate_losses) : 0;
        double expectancy = win_rate * 2.17; // 使用理论盈亏比计算数学期望

        Print("📊 总交易次数：", total_completed, " 次（排除手续费统计）");
        Print("✅ 盈利交易：", accurate_wins, " 次");
        Print("❌ 亏损交易：", accurate_losses, " 次");
        Print("🎯 当前胜率：", DoubleToString(win_rate, 2), "%");
        Print("💰 总盈利：", DoubleToString(accurate_total_profit, 2), " USD");
        Print("💸 总亏损：", DoubleToString(accurate_total_loss, 2), " USD");
        Print("📈 盈亏比：", DoubleToString(profit_factor, 2));
        Print("💵 平均盈利：", DoubleToString(avg_win, 2), " USD");
        Print("💸 平均亏损：", DoubleToString(avg_loss, 2), " USD");
        Print("🧮 数学期望：", DoubleToString(expectancy, 2), "%");
        Print("🏆 最大盈利：", DoubleToString(stats.max_profit, 2), " USD");
        Print("💔 最大亏损：", DoubleToString(stats.max_loss, 2), " USD");
        Print("🔥 连续盈利：", stats.consecutive_wins, " 次 (最大:", stats.max_consecutive_wins, ")");
        Print("❄️ 连续亏损：", stats.consecutive_losses, " 次 (最大:", stats.max_consecutive_losses, ")");

        // 策略评级
        string rating = "未知";
        if(expectancy > 120) rating = "优秀";
        else if(expectancy > 100) rating = "良好";
        else if(expectancy > 80) rating = "一般";
        else rating = "需要改进";

        Print("⭐ 策略评级：", rating, " (期望值:", DoubleToString(expectancy, 2), "%)");
    }
    else
    {
        Print("📊 暂无交易记录");
    }

    // 信号统计
    Print("================== 信号统计分析 ==================");
    Print("📡 波动异常信号：", stats.volatility_signals, " 次");
    Print("📊 成交量异常信号：", stats.volume_signals, " 次");
    Print("🚫 被过滤信号：", stats.filtered_signals, " 次");

    int total_signals = total_completed + stats.filtered_signals;
    if(total_signals > 0)
    {
        double signal_efficiency = (double)total_completed / total_signals * 100;
        Print("🎯 信号转换率：", DoubleToString(signal_efficiency, 2), "% (", total_completed, "/", total_signals, ")");
    }

    Print("🔍 RSI过滤次数：", stats.rsi_filtered, " 次");
    Print("📈 EMA过滤次数：", stats.ema_filtered, " 次");

    Print("====================================================");
}

//+------------------------------------------------------------------+
//| 移动止损更新                                                      |
//+------------------------------------------------------------------+
void UpdateTrailingStop()
{
    // 修改说明：移动止损优先运算 - 快速检查持仓状态
    if(!PositionSelect(Symbol())) return;

    // 修改说明：优先获取关键价格信息，确保实时性
    double current_bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
    double current_ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double point = SymbolInfoDouble(Symbol(), SYMBOL_POINT);

    // 获取持仓信息
    double local_position_open_price = PositionGetDouble(POSITION_PRICE_OPEN);  // 修改说明：重命名局部变量避免与全局变量冲突
    double position_sl = PositionGetDouble(POSITION_SL);
    double position_tp = PositionGetDouble(POSITION_TP);
    ulong local_position_ticket = PositionGetInteger(POSITION_TICKET);  // 修改说明：重命名局部变量避免与全局变量冲突
    ENUM_POSITION_TYPE position_type = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    // 修改说明：检查是否为新持仓，如果是则重置移动止损状态
    if(local_position_ticket != current_position_ticket)  // 修改说明：比较当前持仓票号与全局记录的票号
    {
        current_position_ticket = local_position_ticket;  // 修改说明：更新全局记录的当前持仓票号
        // 修改说明：所有交易都使用相同的直接移动止损逻辑
        Print("🔄 新持仓检测：开始移动止损跟踪 (票号:", local_position_ticket, ")");  // 修改说明：使用重命名后的局部变量
    }

    double new_sl = 0;
    bool should_modify = false;

    // 修改说明：已移除保本保护检查
    // if(Breakeven_Protection && local_position_ticket == position_ticket && !breakeven_activated)  // 已移除保本保护功能
    // {
    //     CheckBreakevenProtection(position_type, current_bid, current_ask, local_position_open_price, point);
    // }

    if(position_type == POSITION_TYPE_BUY)
    {
        // 修改说明：做多移动止损，已移除保本保护逻辑
        // 直接使用常规移动止损
        new_sl = current_bid - Trailing_Stop_Distance_Buy * point;

        // 只有当新止损比当前止损更有利时才修改
        if(new_sl > position_sl + Trailing_Stop_Step_Buy * point)
        {
            should_modify = true;
            // 修改说明：移动止损优化 - 减少Print频率，避免每次移动都输出日志
            static datetime last_buy_log = 0;
            if(TimeCurrent() - last_buy_log >= 5) // 每5秒最多输出一次
            {
                Print("🛡️ 做多移动止损：新止损 ", new_sl, " (距离", Trailing_Stop_Distance_Buy, "点)");  // 修改说明：已移除保本保护状态显示
                last_buy_log = TimeCurrent();
            }
        }
    }
    else if(position_type == POSITION_TYPE_SELL)
    {
        // 修改说明：做空移动止损，已移除保本保护逻辑
        // 直接使用常规移动止损
        new_sl = current_ask + Trailing_Stop_Distance_Sell * point;

        // 只有当新止损比当前止损更有利时才修改
        if(position_sl == 0 || new_sl < position_sl - Trailing_Stop_Step_Sell * point)
        {
            should_modify = true;
            // 修改说明：移动止损优化 - 减少Print频率，避免每次移动都输出日志
            static datetime last_sell_log = 0;
            if(TimeCurrent() - last_sell_log >= 5) // 每5秒最多输出一次
            {
                Print("🛡️ 做空移动止损：新止损 ", new_sl, " (距离", Trailing_Stop_Distance_Sell, "点)");  // 修改说明：已移除保本保护状态显示
                last_sell_log = TimeCurrent();
            }
        }
    }

    if(should_modify)
    {
        MqlTradeRequest request = {};
        MqlTradeResult result = {};

        request.action = TRADE_ACTION_SLTP;
        request.position = local_position_ticket;  // 修改说明：使用重命名后的局部变量
        request.sl = NormalizeDouble(new_sl, Digits());
        request.tp = position_tp;

        if(OrderSend(request, result))
        {
            if(result.retcode == TRADE_RETCODE_DONE)
            {
                // 修改说明：显示具体使用的移动止损参数和利润保护状态
                string direction_text = (position_type == POSITION_TYPE_BUY) ? "做多" : "做空";
                int used_distance = (position_type == POSITION_TYPE_BUY) ? Trailing_Stop_Distance_Buy : Trailing_Stop_Distance_Sell;

                // 修改说明：移动止损优化 - 减少成功日志频率
                static datetime last_success_log = 0;
                if(TimeCurrent() - last_success_log >= 10) // 每10秒最多输出一次成功日志
                {
                    Print("✅ ", direction_text, "移动止损成功：新止损 ", new_sl, "，开仓价 ", local_position_open_price, "，距离 ", used_distance, " 点");  // 修改说明：使用重命名后的局部变量
                    last_success_log = TimeCurrent();
                }
            }
            else
            {
                Print("❌ 移动止损失败：错误代码 ", result.retcode);
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 执行反向交易（已移除功能）                                          |
//+------------------------------------------------------------------+
// void ExecuteReverseTrade()  // 已移除反向交易功能
// {
//     // 修改说明：反向交易功能已完全移除

//+------------------------------------------------------------------+
//| 初始化电报通知功能                                                 |
//+------------------------------------------------------------------+
void InitializeTelegramNotification()
{
    Print("📱 开始初始化电报通知...");

    if(!Telegram_Enabled)
    {
        Print("📱 电报通知：已禁用");
        telegram_initialized = false;
        return;
    }

    Print("📱 电报通知：已启用，检查参数...");
    Print("   Token长度: ", StringLen(Telegram_Bot_Token));
    Print("   Chat ID: ", Telegram_Chat_ID);

    if(Telegram_Bot_Token == "" || Telegram_Chat_ID == "")
    {
        Print("⚠️ 电报通知：Token或Chat ID为空，功能已禁用");
        telegram_initialized = false;
        return;
    }

    // 检查WebRequest权限
    Print("📱 检查WebRequest权限...");
    Print("📱 请确认以下设置：");
    Print("   1. 工具->选项->专家顾问->允许WebRequest ✅");
    Print("   2. URL列表包含：https://api.telegram.org ✅");
    Print("   3. 已重启MT5 ✅");

    // 先发送简单测试消息
    Print("📱 发送测试消息...");
    string test_message = "Test";

    if(SendTelegramMessage(test_message))
    {
        Print("📱 测试消息发送成功，发送完整初始化消息...");

        // 发送完整初始化消息
        string init_message = "US30 EA STARTED\n";
        init_message += "===================\n";
        init_message += "Account: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)) + "\n";
        init_message += "Balance: $" + DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE), 2) + "\n";
        init_message += "Equity: $" + DoubleToString(AccountInfoDouble(ACCOUNT_EQUITY), 2) + "\n";
        init_message += "Broker: " + AccountInfoString(ACCOUNT_COMPANY) + "\n";
        init_message += "Start Time: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
        init_message += "===================\n";
        init_message += "Strategy: US30 Trend Following\n";
        init_message += "Notifications: Active";

        SendTelegramMessage(init_message);
        telegram_initialized = true;
        Print("✅ 电报通知：初始化成功");
    }
    else
    {
        telegram_initialized = false;
        Print("❌ 电报通知：初始化失败");
        Print("❌ 交易通知将被禁用，但EA继续正常运行");
        Print("❌ 解决方案：");
        Print("   1. 确保MT5有网络连接权限");
        Print("   2. 检查防火墙是否阻止MT5");
        Print("   3. 尝试重启MT5");
        Print("   4. 如果在VPS上，检查VPS网络设置");
    }
}

//+------------------------------------------------------------------+
//| 发送电报消息                                                       |
//+------------------------------------------------------------------+
bool SendTelegramMessage(string message)
{
    if(!Telegram_Enabled || Telegram_Bot_Token == "" || Telegram_Chat_ID == "")
    {
        Print("❌ 电报发送失败：参数检查失败");
        Print("   Telegram_Enabled: ", Telegram_Enabled);
        Print("   Token长度: ", StringLen(Telegram_Bot_Token));
        Print("   Chat_ID: ", Telegram_Chat_ID);
        return false;
    }

    string url = "https://api.telegram.org/bot" + Telegram_Bot_Token + "/sendMessage";
    string headers = "Content-Type: application/x-www-form-urlencoded\r\n";

    Print("📱 准备发送电报消息: ", StringSubstr(message, 0, 50), "...");

    // 简化URL编码，避免复杂字符问题
    string encoded_message = message;
    StringReplace(encoded_message, " ", "%20");
    StringReplace(encoded_message, "\n", "%0A");
    StringReplace(encoded_message, "&", "%26");
    StringReplace(encoded_message, "=", "%3D");
    StringReplace(encoded_message, "+", "%2B");
    StringReplace(encoded_message, "#", "%23");
    StringReplace(encoded_message, "?", "%3F");

    string post_data = "chat_id=" + Telegram_Chat_ID + "&text=" + encoded_message;

    Print("📱 POST数据长度: ", StringLen(post_data));

    char result[];
    char data[];
    StringToCharArray(post_data, data, 0, StringLen(post_data));

    int timeout = 10000; // 增加到10秒超时
    Print("📱 发送WebRequest到: ", url);

    int res = WebRequest("POST", url, headers, timeout, data, result, headers);

    Print("📱 WebRequest返回代码: ", res);

    if(res == 200)
    {
        Print("✅ 电报消息发送成功");
        return true;
    }
    else if(res == -1)
    {
        Print("❌ 电报消息发送失败：网络连接错误 (-1)");
        Print("   请检查WebRequest权限和网络连接");
        Print("❌ 常见解决方案：");
        Print("   1. MT5->工具->选项->专家顾问->勾选'允许WebRequest'");
        Print("   2. 在URL列表添加：https://api.telegram.org");
        Print("   3. 完全重启MT5");
        Print("   4. 检查网络连接和防火墙");
        Print("   5. 如果问题持续，请将Telegram_Enabled设为false");
        return false;
    }
    else
    {
        Print("❌ 电报消息发送失败，HTTP代码：", res);
        if(ArraySize(result) > 0)
        {
            string response = CharArrayToString(result);
            Print("   服务器响应: ", response);
        }
        return false;
    }
}

//+------------------------------------------------------------------+
//| 发送交易完成电报通知                                               |
//+------------------------------------------------------------------+
void SendTradeTelegramNotification(bool is_profit, double net_result, double gross_result, double commission)
{
    if(!telegram_initialized)
    {
        Print("📱 电报通知跳过：未初始化 (telegram_initialized=", telegram_initialized, ")");
        return;
    }

    Print("📱 准备发送交易通知...");

    // 获取最近完成的交易信息
    string trade_details = GetLastTradeDetails();

    string message = "";
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double daily_profit_loss = current_balance - initial_balance;

    if(is_profit)
    {
        message = "US30 TRADE PROFIT\n";
        message += "===================\n";
        message += "Net Profit: +$" + DoubleToString(net_result, 2) + "\n";
        message += "Return: +" + DoubleToString((net_result/current_balance)*100, 2) + "%\n";
    }
    else
    {
        message = "US30 TRADE LOSS\n";
        message += "===================\n";
        message += "Net Loss: $" + DoubleToString(net_result, 2) + "\n";
        message += "Return: " + DoubleToString((net_result/current_balance)*100, 2) + "%\n";
    }

    // 添加真实交易详情
    if(trade_details != "")
    {
        message += trade_details;
    }

    message += "===================\n";
    message += "Account Status:\n";
    message += "Balance: $" + DoubleToString(current_balance, 2) + "\n";
    message += "Equity: $" + DoubleToString(current_equity, 2) + "\n";
    message += "-------------------\n";
    message += "Daily Stats:\n";
    message += "Trades: " + IntegerToString(daily_trade_count) + "\n";

    if(daily_profit_loss >= 0)
        message += "Daily P&L: +$" + DoubleToString(daily_profit_loss, 2) + "\n";
    else
        message += "Daily P&L: $" + DoubleToString(daily_profit_loss, 2) + "\n";

    message += "Time: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES);

    Print("📱 交易通知内容: ", message);

    // 尝试发送，失败也不影响交易
    if(SendTelegramMessage(message))
    {
        Print("✅ 交易通知发送成功");
    }
    else
    {
        Print("❌ 交易通知发送失败，但不影响正常交易");
    }
}

//+------------------------------------------------------------------+
//| 获取最近完成交易的详细信息                                         |
//+------------------------------------------------------------------+
string GetLastTradeDetails()
{
    string details = "";

    // 获取交易历史
    if(!HistorySelect(TimeCurrent() - 3600, TimeCurrent())) // 查看最近1小时的历史
        return details;

    int total_deals = HistoryDealsTotal();
    if(total_deals <= 0)
        return details;

    // 查找最近的交易
    for(int i = total_deals - 1; i >= 0; i--)
    {
        ulong deal_ticket = HistoryDealGetTicket(i);
        if(deal_ticket <= 0) continue;

        // 检查是否是我们的EA的交易
        long magic = HistoryDealGetInteger(deal_ticket, DEAL_MAGIC);
        if(magic != 12345) continue; // 我们EA的魔术号

        // 获取交易详情
        string symbol = HistoryDealGetString(deal_ticket, DEAL_SYMBOL);
        if(symbol != Symbol()) continue; // 只关心当前品种

        long deal_type = HistoryDealGetInteger(deal_ticket, DEAL_TYPE);
        if(deal_type != DEAL_TYPE_BUY && deal_type != DEAL_TYPE_SELL) continue;

        double volume = HistoryDealGetDouble(deal_ticket, DEAL_VOLUME);
        double price = HistoryDealGetDouble(deal_ticket, DEAL_PRICE);
        double profit = HistoryDealGetDouble(deal_ticket, DEAL_PROFIT);
        double commission = HistoryDealGetDouble(deal_ticket, DEAL_COMMISSION);
        double swap = HistoryDealGetDouble(deal_ticket, DEAL_SWAP);
        datetime deal_time = (datetime)HistoryDealGetInteger(deal_ticket, DEAL_TIME);

        // 构建交易详情
        details += "Trade Details:\n";
        details += "Type: " + (deal_type == DEAL_TYPE_BUY ? "BUY" : "SELL") + "\n";
        details += "Volume: " + DoubleToString(volume, 2) + " lots\n";
        details += "Price: " + DoubleToString(price, 2) + "\n";
        details += "Gross P&L: $" + DoubleToString(profit, 2) + "\n";

        if(commission != 0)
            details += "Commission: $" + DoubleToString(commission, 2) + "\n";
        if(swap != 0)
            details += "Swap: $" + DoubleToString(swap, 2) + "\n";

        double net_profit = profit + commission + swap;
        details += "Net P&L: $" + DoubleToString(net_profit, 2) + "\n";
        details += "Deal Time: " + TimeToString(deal_time, TIME_MINUTES) + "\n";

        break; // 只取最近的一笔交易
    }

    return details;
}

//+------------------------------------------------------------------+
//| 检查并发送每日电报报告                                             |
//+------------------------------------------------------------------+
void CheckDailyTelegramReport()
{
    if(!Telegram_Enabled || !Telegram_Daily_Report || !telegram_initialized)
        return;

    datetime current_time = TimeCurrent();
    MqlDateTime dt;
    TimeToStruct(current_time, dt);

    // 检查是否到了每日报告时间
    if(dt.hour == Daily_Report_Hour && dt.min < 5) // 在指定小时的前5分钟内
    {
        // 检查今天是否已经发送过报告
        MqlDateTime last_report_dt;
        TimeToStruct(last_daily_report_time, last_report_dt);

        if(last_report_dt.day != dt.day || last_report_dt.mon != dt.mon || last_report_dt.year != dt.year)
        {
            SendDailyTelegramReport();
            last_daily_report_time = current_time;
        }
    }
}

//+------------------------------------------------------------------+
//| 发送每日电报报告                                                   |
//+------------------------------------------------------------------+
void SendDailyTelegramReport()
{
    if(!telegram_initialized)
        return;

    string message = "US30 DAILY REPORT\n";
    message += "===========================\n";
    message += "Date: " + TimeToString(TimeCurrent(), TIME_DATE) + "\n";
    message += "===========================\n";

    // 账户基本信息
    double current_balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double current_equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double free_margin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

    message += "Account Info:\n";
    message += "Account: " + IntegerToString(AccountInfoInteger(ACCOUNT_LOGIN)) + "\n";
    message += "Balance: $" + DoubleToString(current_balance, 2) + "\n";
    message += "Equity: $" + DoubleToString(current_equity, 2) + "\n";
    message += "Free Margin: $" + DoubleToString(free_margin, 2) + "\n";
    message += "---------------------------\n";

    // 今日盈亏
    double daily_profit_loss = current_balance - initial_balance;
    double daily_return_pct = (daily_profit_loss / initial_balance) * 100;

    message += "Daily Performance:\n";
    if(daily_profit_loss >= 0)
    {
        message += "Daily P&L: +$" + DoubleToString(daily_profit_loss, 2) + "\n";
        message += "Return: +" + DoubleToString(daily_return_pct, 2) + "%\n";
    }
    else
    {
        message += "Daily P&L: $" + DoubleToString(daily_profit_loss, 2) + "\n";
        message += "Return: " + DoubleToString(daily_return_pct, 2) + "%\n";
    }
    message += "---------------------------\n";

    // 交易统计
    message += "Trading Stats:\n";
    message += "Daily Trades: " + IntegerToString(daily_trade_count) + "\n";
    message += "Trade Attempts: " + IntegerToString(daily_trade_attempts) + "\n";

    // 计算胜率
    int total_trades = stats.winning_trades + stats.losing_trades;
    if(total_trades > 0)
    {
        double win_rate = (double)stats.winning_trades / total_trades * 100;
        message += "Win Rate: " + DoubleToString(win_rate, 1) + "%\n";
        message += "Winning Trades: " + IntegerToString(stats.winning_trades) + "\n";
        message += "Losing Trades: " + IntegerToString(stats.losing_trades) + "\n";

        if(stats.winning_trades > 0 && stats.losing_trades > 0)
        {
            double avg_win = stats.total_profit / stats.winning_trades;
            double avg_loss = stats.total_loss / stats.losing_trades;
            double profit_factor = avg_win / avg_loss;
            message += "Profit Factor: 1:" + DoubleToString(profit_factor, 2) + "\n";
        }
    }
    else
    {
        message += "No trades today\n";
    }

    message += "---------------------------\n";
    message += "Report Time: " + TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES) + "\n";
    message += "EA Status: Running\n";
    message += "===========================";

    SendTelegramMessage(message);
}
//     return;
// }
